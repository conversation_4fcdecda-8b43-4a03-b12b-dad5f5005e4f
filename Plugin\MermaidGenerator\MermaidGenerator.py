#!/usr/bin/env python3
"""
MermaidGenerator 插件 - 重构版本
功能：将 Mermaid 代码渲染为 PNG 图片
设计原则：简洁、健壮、可靠
"""
import sys
import json
import os
import subprocess
import tempfile
import base64
import re
from pathlib import Path

class MermaidRenderer:
    """Mermaid 渲染器 - 核心渲染逻辑"""
    
    def __init__(self):
        self.supported_types = [
            'flowchart', 'graph', 'sequenceDiagram', 'classDiagram', 
            'stateDiagram', 'erDiagram', 'journey', 'gantt', 'pie'
        ]
        self.dangerous_keywords = ['mindmap', 'root((']
    
    def is_dangerous_syntax(self, code):
        """检测危险的语法模式"""
        code_lower = code.lower().strip()
        
        # 检测明确的危险关键字
        for keyword in self.dangerous_keywords:
            if keyword in code_lower:
                return True
        
        # 检测缩进结构（思维导图特征）
        lines = code.strip().split('\n')
        if len(lines) > 1:
            has_indentation = any(line.startswith(('  ', '\t')) for line in lines)
            has_arrows = any('-->' in line or '->' in line for line in lines)
            # 有缩进但没有箭头 = 思维导图
            if has_indentation and not has_arrows:
                return True
        
        return False
    
    def convert_to_safe_flowchart(self, code):
        """将危险语法转换为安全的流程图"""
        lines = code.strip().split('\n')
        nodes = []
        
        # 提取文本内容
        for line in lines:
            # 清理行内容
            clean_line = re.sub(r'^[\s\t]*', '', line)  # 移除缩进
            clean_line = re.sub(r'(mindmap|root)', '', clean_line, flags=re.IGNORECASE)
            clean_line = re.sub(r'[()（）]', '', clean_line)  # 移除括号
            clean_line = clean_line.strip()
            
            if clean_line and len(clean_line) > 0:
                nodes.append(clean_line[:30])  # 限制长度
        
        # 生成安全的流程图
        if not nodes:
            return 'flowchart TD\n    A["转换后的图表"]'
        
        result = ['flowchart TD']
        max_nodes = min(len(nodes), 5)  # 最多5个节点
        
        for i in range(max_nodes):
            node_id = chr(65 + i)  # A, B, C, D, E
            result.append(f'    {node_id}["{nodes[i]}"]')
            if i > 0:
                prev_id = chr(65 + i - 1)
                result.append(f'    {prev_id} --> {node_id}')
        
        return '\n'.join(result)
    
    def normalize_code(self, code):
        """标准化 Mermaid 代码"""
        # 检测并处理危险语法
        if self.is_dangerous_syntax(code):
            print("⚠️ 检测到不安全的语法，已自动转换为流程图", file=sys.stderr)
            return self.convert_to_safe_flowchart(code)
        
        # 清理代码
        lines = [line.strip() for line in code.strip().split('\n') if line.strip()]
        
        # 检查图表类型
        has_type = any(
            line.lower().startswith(chart_type.lower()) 
            for line in lines 
            for chart_type in self.supported_types
        )
        
        # 如果没有类型声明，添加默认类型
        if not has_type:
            if any('-->' in line for line in lines):
                lines.insert(0, 'flowchart TD')
            else:
                # 包装为简单图表
                return f'flowchart TD\n    A["{code[:40]}..."]'
        
        return '\n'.join(lines)
    
    def find_mermaid_cli(self):
        """查找 mermaid-cli 可执行文件"""
        possible_commands = ['mmdc', 'mmdc.cmd']
        
        for cmd in possible_commands:
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    return cmd
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        return None
    
    def render_to_png(self, mermaid_code):
        """渲染 Mermaid 代码为 PNG"""
        # 标准化代码
        normalized_code = self.normalize_code(mermaid_code)
        
        # 查找 mermaid-cli
        mmdc_cmd = self.find_mermaid_cli()
        if not mmdc_cmd:
            raise RuntimeError("未找到 mermaid-cli (mmdc)。请安装: npm install -g @mermaid-js/mermaid-cli")
        
        # 创建临时文件
        with tempfile.TemporaryDirectory() as temp_dir:
            input_file = Path(temp_dir) / 'input.mmd'
            output_file = Path(temp_dir) / 'output.png'
            
            # 写入 Mermaid 代码
            input_file.write_text(normalized_code, encoding='utf-8')
            
            # 执行渲染
            try:
                result = subprocess.run([
                    mmdc_cmd, '-i', str(input_file), '-o', str(output_file),
                    '--backgroundColor', 'white'
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode != 0:
                    error_msg = result.stderr or result.stdout or "未知错误"
                    raise RuntimeError(f"渲染失败: {error_msg}")
                
                # 读取生成的图片
                if not output_file.exists():
                    raise RuntimeError("图片文件未生成")
                
                with open(output_file, 'rb') as f:
                    image_data = f.read()
                
                return base64.b64encode(image_data).decode('utf-8')
                
            except subprocess.TimeoutExpired:
                raise RuntimeError("渲染超时")

def main():
    """主函数"""
    try:
        # 读取输入
        input_data = sys.stdin.read().strip()
        if not input_data:
            raise ValueError("未接收到输入数据")
        
        # 解析参数
        try:
            params = json.loads(input_data)
        except json.JSONDecodeError:
            raise ValueError("输入数据不是有效的 JSON")
        
        chart_code = params.get('chart_code')
        if not chart_code:
            raise ValueError("缺少 chart_code 参数")
        
        # 渲染图表
        renderer = MermaidRenderer()
        base64_image = renderer.render_to_png(chart_code)
        
        # 构建 HTML 结果
        html_result = f'''<p><strong>Mermaid 图表生成成功！</strong></p>
<img src="data:image/png;base64,{base64_image}" alt="Generated Mermaid Chart" width="400" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px; margin: 10px 0;" />
<p><small>图表大小: {len(base64_image)} 字符的 Base64 编码</small></p>'''
        
        # 输出结果
        result = {"status": "success", "result": html_result}
        print(json.dumps(result, ensure_ascii=False))
        
    except Exception as e:
        # 输出错误
        error_result = {"status": "error", "error": f"插件执行失败: {str(e)}"}
        print(json.dumps(error_result, ensure_ascii=False))
        sys.exit(1)

if __name__ == "__main__":
    main()
