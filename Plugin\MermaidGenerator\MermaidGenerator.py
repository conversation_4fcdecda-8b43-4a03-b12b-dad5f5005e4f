import sys
import json
import os
import subprocess
import tempfile
import base64

def main():
    # 使用临时目录来处理文件，确保线程安全和自动清理
    with tempfile.TemporaryDirectory() as temp_dir:
        input_file = os.path.join(temp_dir, 'input.mmd')
        output_file = os.path.join(temp_dir, 'output.png')

        try:
            # 1. 从 stdin 读取输入
            input_data = sys.stdin.readline()
            if not input_data:
                raise ValueError("没有从 stdin 接收到数据。")
            
            params = json.loads(input_data)
            chart_code = params.get("chart_code")

            if not chart_code:
                raise ValueError("请求中缺少 'chart_code' 参数。")

            # 2. 将Mermaid代码写入临时文件
            with open(input_file, 'w', encoding='utf-8') as f:
                f.write(chart_code)

            # 3. 调用 mermaid-cli (mmdc) 执行渲染
            #    -i: 输入文件, -o: 输出文件
            # 在 Windows 上，需要使用完整路径或通过 PowerShell 调用
            if os.name == 'nt':  # Windows
                # 尝试多种可能的路径
                possible_paths = [
                    'mmdc',  # 如果在 PATH 中
                    'mmdc.cmd',  # Windows 命令文件
                    os.path.expanduser('~\\AppData\\Roaming\\npm\\mmdc.cmd'),  # npm 全局安装路径
                    os.path.expanduser('~\\AppData\\Roaming\\npm\\mmdc.ps1'),  # PowerShell 脚本
                ]

                mmdc_cmd = None
                for path in possible_paths:
                    try:
                        # 测试命令是否可用
                        test_result = subprocess.run([path, '--version'],
                                                   capture_output=True,
                                                   text=True,
                                                   timeout=5)
                        if test_result.returncode == 0:
                            mmdc_cmd = path
                            break
                    except (FileNotFoundError, subprocess.TimeoutExpired):
                        continue

                if mmdc_cmd is None:
                    # 如果都找不到，尝试通过 PowerShell 调用
                    command = ['powershell', '-Command', f'mmdc -i "{input_file}" -o "{output_file}"']
                else:
                    command = [mmdc_cmd, '-i', input_file, '-o', output_file]
            else:
                # Linux/Mac
                command = ['mmdc', '-i', input_file, '-o', output_file]
            
            # 使用 subprocess.run 来执行命令
            # capture_output=True 可以捕获 stdout 和 stderr
            # text=True 让输出以文本形式返回
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                encoding='utf-8',
                check=False  # 我们手动检查返回码，所以不让它自动抛出异常
            )

            # 4. 检查执行结果
            if result.returncode != 0:
                # 如果返回码非0，表示渲染失败
                error_message = result.stderr or "mmdc 执行失败，但没有提供错误信息。"
                # 清理错误信息中可能存在的无关路径
                error_message = error_message.replace(temp_dir, '...')
                raise RuntimeError(f"Mermaid渲染失败: {error_message}")

            # 5. 如果成功，读取图片并转换为Base64
            with open(output_file, 'rb') as f:
                img_base64 = base64.b64encode(f.read()).decode('utf-8')
            
            data_uri = f"data:image/png;base64,{img_base64}"

            # 6. 构建返回结果
            # 检查生成的图片大小
            img_size = len(img_base64)

            # 根据前端代码分析，直接返回包含图片的HTML字符串
            # 前端的 imageHandler.js 会自动处理 <img> 标签
            result_html = f'''<p><strong>Mermaid图表生成成功！</strong></p>
<img src="data:image/png;base64,{img_base64}" alt="Generated Mermaid Chart" width="400" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px; margin: 10px 0;" />
<p><small>图表大小: {img_size} 字符的Base64编码</small></p>'''

            output = {
                "status": "success",
                "result": result_html
            }

        except FileNotFoundError:
            # 如果 mmdc 命令不存在
            output = {"status": "error", "error": "命令执行失败: 'mmdc' 未找到。请确保 @mermaid-js/mermaid-cli 已正确安装在服务器上。"}
        except Exception as e:
            output = {"status": "error", "error": f"插件执行失败: {str(e)}"}

    # 7. 将最终结果打印到 stdout
    # 使用 ensure_ascii=True 避免 Windows 控制台编码问题
    result_json = json.dumps(output, ensure_ascii=True)
    print(result_json, file=sys.stdout)
    sys.stdout.flush()

if __name__ == "__main__":
    main()