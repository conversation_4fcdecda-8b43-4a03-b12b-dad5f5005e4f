{"name": "cherry-var", "version": "1.0.0", "main": "index.js", "scripts": {"test": ""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@tavily/core": "^0.5.2", "axios": "^1.11.0", "basic-auth": "^2.0.1", "cheerio": "^1.1.0", "chinese-lunar-calendar": "^1.0.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "flatted": "^3.3.3", "ioredis": "^5.6.1", "js-tiktoken": "^1.0.20", "jsdom": "^24.0.0", "md5": "^2.3.0", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "node-schedule": "^2.1.1", "pm2": "^5.0.0", "puppeteer": "^22.15.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-anonymize-ua": "^2.4.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "rss-parser": "^3.13.0", "user-agents": "^1.1.597", "uuid": "^9.0.0", "winston": "^3.17.0", "ws": "^8.17.0"}}