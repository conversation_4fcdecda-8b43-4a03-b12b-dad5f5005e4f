{"name": "SunoGen", "displayName": "Suno AI 音乐生成", "version": "0.1.0", "description": "使用 Suno API 生成原创歌曲。支持通过歌词、风格、标题进行自定义创作，或通过描述获取灵感创作，还可以继续生成已有的歌曲片段。", "pluginType": "synchronous", "entryPoint": {"command": "node SunoGen.mjs"}, "communication": {"protocol": "stdio", "timeout": 360000}, "configSchema": {"SunoKey": "string", "SunoApiBaseUrl": "string"}, "capabilities": {"invocationCommands": [{"command": "generate_song", "description": "调用 Suno API 生成一首歌曲。\n**重要提示：歌曲生成可能需要一些时间（通常10-30秒，但有时可能更长），请在调用成功后告知用户任务已提交，并提醒他们需要耐心等待。当获取到音频链接后，请使用HTML的 `<audio>` 标签和下载链接格式将其呈现给用户。**\n\n**参数说明 (请严格按照以下格式和参数名提供，不要包含任何未列出的参数):**\n\n**必需的通用参数:**\n1. tool_name:「始」SunoGen「末」\n2. command:「始」generate_song「末」\n\n**模式一：自定义歌词创作 (Custom Mode)**\n   - 如果使用此模式，以下三个参数 `prompt`, `tags`, `title` 都是必需的。\n3. prompt:「始」[歌词内容，可以包含 [Verse], [Chorus] 等标记]「末」 (必需)\n4. tags:「始」[音乐风格标签，多个用逗号分隔，例如 'pop, acoustic, upbeat']「末」 (必需)\n5. title:「始」[歌曲标题]「末」 (必需)\n\n**模式二：灵感描述创作 (Inspiration Mode)**\n   - 如果使用此模式，`gpt_description_prompt` 是必需的，此时 `prompt`, `tags`, `title` 变为可选（但如果提供，API也可能使用它们）。\n3. gpt_description_prompt:「始」[对歌曲的整体描述，例如 '一首关于夏天和海滩的欢快流行歌曲']「末」 (必需)\n4. prompt:「始」[可选的歌词提示]「末」 (可选)\n5. tags:「始」[可选的风格标签]「末」 (可选)\n6. title:「始」[可选的歌曲标题]「末」 (可选)\n\n**通用可选参数 (适用于以上两种模式):**\n* mv:「始」[模型版本]「末」 (可选, 值必须是 'chirp-v3-0', 'chirp-v3-5', 或 'chirp-v4'. 默认为 'chirp-v4')\n\n**模式三：继续生成已有歌曲 (Continuation Mode)**\n   - 如果使用此模式，`task_id`, `continue_at`, `continue_clip_id` 都是必需的。\n3. task_id:「始」[要继续的歌曲的任务ID]「末」 (必需)\n4. continue_at:「始」[从歌曲的哪个时间点开始继续，单位秒，数字]「末」 (必需)\n5. continue_clip_id:「始」[要继续的歌曲片段的ID]「末」 (必需)\n\n**成功时返回 JSON:** { \"status\": \"success\", \"result\": { \"audio_url\": \"...\", \"title\": \"...\", \"style_tags\": \"...\", \"image_url\": \"...\", \"suno_response_text\": \"...\" }, \"messageForAI\": \"歌曲已生成！请将音频链接...\" }\n**失败时返回 JSON:** { \"status\": \"error\", \"error\": \"错误信息...\" }\n\n**调用示例 (歌词模式，不需要定义make_instrumental):**\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SunoGen「末」,\ncommand:「始」generate_song「末」,\nprompt:「始」[Verse 1]\\nSunlight on my face\\nA gentle, warm embrace「末」,\ntags:「始」acoustic, pop, happy「末」,\ntitle:「始」Sunny Days「末」,\nmv:「始」chirp-v4「末」,\n<<<[END_TOOL_REQUEST]>>>\n\n**调用示例 (灵感模式，无歌词模式):**\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SunoGen「末」,\ncommand:「始」generate_song「末」,\ngpt_description_prompt:「始」一首关于星空和梦想的安静钢琴曲「末」,\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例 (自定义模式):**\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SunoGen「末」,\ncommand:「始」generate_song「末」,\nprompt:「始」[Verse 1]\\nSunlight on my face\\nA gentle, warm embrace「末」,\ntags:「始」acoustic, pop, happy「末」,\ntitle:「始」Sunny Days「末」,\nmv:「始」chirp-v4「末」,\nmake_instrumental:「始」false「末」\n<<<[END_TOOL_REQUEST]>>>\n\n**调用示例 (灵感模式):**\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SunoGen「末」,\ncommand:「始」generate_song「末」,\ngpt_description_prompt:「始」一首关于星空和梦想的安静钢琴曲「末」,\nmake_instrumental:「始」true「末」\n<<<[END_TOOL_REQUEST]>>>\n\n**调用示例 (继续生成模式):**\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SunoGen「末」,\ncommand:「始」generate_song「末」,\ntask_id:「始」your_previous_task_id「末」,\ncontinue_at:「始」60「末」,\ncontinue_clip_id:「始」your_previous_clip_id「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}