{"manifestVersion": "1.0.0", "name": "ImageProcessor", "version": "1.0.0", "displayName": "图像信息提取器", "description": "处理用户消息中的图像数据，调用多模态模型提取图像信息，并将其替换或附加到消息文本中。同时管理图像描述的缓存。", "author": "System", "pluginType": "messagePreprocessor", "entryPoint": {"type": "nodejs", "script": "image-processor.js"}, "communication": {"protocol": "direct"}, "capabilities": {}, "configSchema": {"API_URL": "string", "API_Key": "string", "ImageModel": "string", "ImagePrompt": "string", "ImageModelOutputMaxTokens": "integer", "ImageModelThinkingBudget": "integer", "ImageModelAsynchronousLimit": "integer", "ImageInsertPrompt": "string", "DebugMode": "boolean"}, "lifecycle": {"loadCache": "initialize", "saveCache": "shutdown"}}