{"manifestVersion": "1.0.0", "name": "OpenAIDrawer", "version": "1.0.0", "displayName": "AI绘画插件", "description": "通过一个兼容OpenAI格式的API来调用AI绘画模型生成图片。", "author": "Your Name", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node drawer.js"}, "communication": {"protocol": "stdio", "timeout": 180000}, "configSchema": {"OPENAI_API_KEY": {"type": "string", "description": "你的绘画API的密钥。"}, "OPENAI_API_BASE_URL": {"type": "string", "description": "你的绘画API的根地址 (例如: https://api.your-provider.com/v1)。"}, "OPENAI_IMAGE_MODEL": {"type": "string", "description": "要使用的绘画模型名称。", "default": "dall-e-3"}, "OPENAI_IMAGE_SIZE": {"type": "string", "description": "生成图片的尺寸。", "default": "1024x1024"}, "OPENAI_IMAGE_QUALITY": {"type": "string", "description": "生成图片的质量 (standard or hd)。", "default": "standard"}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "DrawImage", "description": "调用AI绘画模型来生成一张或多张图片。当你需要进行绘画创作时，请使用此工具.\n参数:\n- prompt (字符串, 必需): 请详细描述你想要生成的画面内容。\n- count (数字, 可选, 默认 1): 需要生成的图片数量。\n- style (字符串, 可选, 'vivid' 或 'natural', 默认 'vivid'): 生成图片的风格。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」OpenAIDrawer「末」,\nprompt:「始」你想要画的具体内容「末」,\ncount:「始」1「末」,\nstyle:「始」vivid「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "<<<[TOOL_REQUEST]>>>\ntool_name:「始」OpenAIDrawer「末」,\nprompt:「始」一只可爱的暹罗猫，坐在窗台上，看着窗外的雪景，动漫风格。「末」,\ncount:「始」2「末」,\nstyle:「始」natural「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}