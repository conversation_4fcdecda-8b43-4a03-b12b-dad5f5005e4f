# AgentAssistant Plugin Configuration Example
# This file provides an example of how to configure the AgentAssistant plugin.
# Copy this file to 'config.env' and fill in your actual values.

# --- General Plugin Settings ---
# Maximum number of conversation rounds to keep in history for each session.
AGENT_ASSISTANT_MAX_HISTORY_ROUNDS=10
# Time-to-live for session context in hours. After this period of inactivity, the context will be cleared.
AGENT_ASSISTANT_CONTEXT_TTL_HOURS=24

# --- Shared System Prompt ---
# This prompt is appended to the end of every individual agent's system prompt.
# It's useful for providing global instructions or context to all agents.
AGENT_ALL_SYSTEM_PROMPT="You are a helpful assistant part of a multi-agent system. Please be concise and professional."

# --- Agent Definitions ---
# Define your agents below. Each agent needs a unique BASE_NAME in uppercase ASCII.
# For each agent, you must define at least MODEL_ID and DISPLAY_NAME.
# Other parameters like SYSTEM_PROMPT, MAX_OUTPUT_TOKENS, and TEMPERATURE are optional and have defaults.

# 1. Technical Support Agent
AGENT_SUPPORT_MODEL_ID="your_model_id_here"
AGENT_SUPPORT_DISPLAY_NAME="SupportAgent"
AGENT_SUPPORT_SYSTEM_PROMPT="You are a technical support agent named {{AgentName}}. Your goal is to help users solve their technical issues with the product."
AGENT_SUPPORT_MAX_OUTPUT_TOKENS=8192
AGENT_SUPPORT_TEMPERATURE=0.5
AGENT_SUPPORT_DESCRIPTION="A technical support agent who can answer questions about product features and troubleshoot issues."

# 2. Sales Inquiry Agent
AGENT_SALES_MODEL_ID="your_model_id_here"
AGENT_SALES_DISPLAY_NAME="SalesAgent"
AGENT_SALES_SYSTEM_PROMPT="You are a sales agent named {{AgentName}}. Your goal is to answer questions about pricing, plans, and product capabilities to potential customers."
AGENT_SALES_MAX_OUTPUT_TOKENS=8192
AGENT_SALES_TEMPERATURE=0.7
AGENT_SALES_DESCRIPTION="A sales agent who can provide information about product pricing and plans."

# 3. General Assistant
AGENT_GENERAL_MODEL_ID="your_model_id_here"
AGENT_GENERAL_DISPLAY_NAME="GeneralAssistant"
# This agent will use the default system prompt if one is not provided.
AGENT_GENERAL_DESCRIPTION="A general-purpose assistant for a wide range of tasks."
