#!/usr/bin/env python3
"""
测试脚本：验证 MermaidGenerator 插件的思维导图规避机制
"""
import json
import subprocess
import sys
import os

def test_mermaid_code(chart_code, description, should_detect_mindmap=False):
    """测试单个 Mermaid 代码"""
    print(f"\n🧪 测试: {description}")
    print(f"📝 代码: {chart_code[:60]}...")
    
    try:
        # 执行插件
        result = subprocess.run(
            [sys.executable, 'MermaidGenerator.py'],
            input=json.dumps({"chart_code": chart_code}),
            capture_output=True,
            text=True,
            cwd='Plugin/MermaidGenerator'
        )
        
        if result.returncode != 0:
            print(f"❌ 插件执行失败: {result.stderr}")
            return False
        
        # 检查是否有思维导图检测警告
        has_warning = "检测到思维导图语法" in result.stderr
        if should_detect_mindmap and not has_warning:
            print(f"❌ 应该检测到思维导图语法但没有检测到")
            return False
        elif not should_detect_mindmap and has_warning:
            print(f"❌ 不应该检测到思维导图语法但检测到了")
            return False
        
        if has_warning:
            print("⚠️ 检测到思维导图语法，已自动转换")
        
        # 解析输出
        try:
            output = json.loads(result.stdout)
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return False
        
        # 检查结果
        if output.get('status') == 'success':
            result_content = output.get('result', '')
            if '<img' in result_content and 'data:image/png;base64,' in result_content:
                print("✅ 测试通过")
                return True
            else:
                print("❌ 结果中没有有效的图片")
                return False
        else:
            print(f"❌ 插件返回错误: {output.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 MermaidGenerator 插件的思维导图规避机制...")
    print("🎯 目标：确保所有思维导图语法都被检测并转换为安全的流程图")
    
    # 定义测试用例
    test_cases = [
        # 危险的思维导图语法（应该被检测和转换）
        {
            'code': 'mindmap\n  root((学习方法))\n    理解与吸收\n    实践应用\n    总结反思',
            'description': '完整思维导图语法（最危险）',
            'should_detect': True
        },
        {
            'code': 'root((中心主题))\n  理解与吸收\n  实践应用',
            'description': '只有 root 关键字的思维导图',
            'should_detect': True
        },
        {
            'code': '  理解与吸收\n  实践应用\n  总结反思',
            'description': '纯缩进结构（无箭头）',
            'should_detect': True
        },
        {
            'code': '\t学习方法\n\t\t理解与吸收\n\t\t实践应用',
            'description': 'Tab 缩进结构',
            'should_detect': True
        },
        
        # 安全的语法（不应该被检测为思维导图）
        {
            'code': 'flowchart TD\n    A[开始] --> B[理解与吸收]\n    B --> C[实践应用]',
            'description': '标准流程图',
            'should_detect': False
        },
        {
            'code': 'graph TD\n    A --> B\n    B --> C',
            'description': '简单图表',
            'should_detect': False
        },
        {
            'code': 'sequenceDiagram\n    Alice->>Bob: Hello\n    Bob-->>Alice: Hi',
            'description': '序列图',
            'should_detect': False
        },
        {
            'code': 'A --> B\nB --> C',
            'description': '简单节点连接（有箭头）',
            'should_detect': False
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for test_case in test_cases:
        success = test_mermaid_code(
            test_case['code'], 
            test_case['description'],
            test_case['should_detect']
        )
        if success:
            success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 个测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！")
        print("\n✨ MermaidGenerator 插件现在具备:")
        print("- 🛡️ 思维导图语法检测和规避机制")
        print("- 🔄 自动转换为安全的流程图")
        print("- ⚠️ 清晰的警告信息")
        print("- 🎯 精确的语法识别（避免误判）")
        print("- 💪 100% 防止 'No parent could be found' 错误")
        return True
    else:
        print("💥 部分测试失败！")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
