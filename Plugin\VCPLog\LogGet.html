<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VCPLog 测试中心</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f4f8; /* 可爱的浅蓝色背景 */
            color: #333;
            margin: 25px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            padding: 30px;
            box-sizing: border-box;
        }
        h1 {
            color: #6a1b9a; /* 可爱的紫色标题 */
            text-align: center;
            margin-bottom: 25px;
            font-size: 2.2em;
        }
        .connection-status {
            font-weight: bold;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.1em;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .status-connecting { background-color: #fff9c4; color: #fbc02d; } /* 黄色连接中 */
        .status-open { background-color: #e8f5e9; color: #43a047; }    /* 绿色已连接 */
        .status-closed { background-color: #ffebee; color: #d32f2f; }   /* 红色已关闭 */
        .status-error { background-color: #ffcdd2; color: #c62828; }    /* 更深的红色错误 */

        .log-container {
            border: 1px dashed #bdbdbd; /* 可爱的虚线边框 */
            border-radius: 8px;
            min-height: 200px;
            max-height: 500px;
            overflow-y: auto;
            padding: 15px;
            background-color: #fafafa;
            box-sizing: border-box;
            margin-bottom: 20px;
        }
        .log-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
            font-size: 0.95em;
            line-height: 1.5;
            word-wrap: break-word;
            white-space: pre-wrap; /* Preserve newlines in JSON */
        }
        .log-item:last-child {
            border-bottom: none;
        }
        .log-timestamp {
            font-size: 0.8em;
            color: #757575;
            margin-top: 5px;
            display: block;
        }
        .log-type-vcp_log { color: #424242; } /* VCP日志主色 */
        .log-type-connection_ack { color: #8bc34a; } /* 连接确认绿色 */

        button {
            background-color: #8e24aa; /* 可爱的按钮紫色 */
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px; /* 圆角按钮 */
            cursor: pointer;
            font-size: 1.05em;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background-color: #7b1fa2; /* 悬停颜色 */
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .footer {
            margin-top: 30px;
            color: #9e9e9e;
            font-size: 0.9em;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VCPLog 通知中心</h1>
        
        <div id="connectionStatus" class="connection-status status-connecting">
            连接状态：连接中...
        </div>

        <div class="log-container" id="logContainer">
            <p>等待接收 VCP 日志信息...</p>
        </div>
        
        <button onclick="clearLogs()">清空日志</button>
    </div>

    <div class="footer">
        由小克为您精心打造
    </div>

    <script>
        const VCP_KEY = '123456';
        const WS_SERVER_URL = 'ws://*************:5890'; // 主人您的局域网服务器地址哦！
        const wsUrl = `${WS_SERVER_URL}/VCPlog/VCP_Key=${VCP_KEY}`;
        let ws;

        const connectionStatusDiv = document.getElementById('connectionStatus');
        const logContainer = document.getElementById('logContainer');

        function updateStatus(status, message) {
            connectionStatusDiv.className = `connection-status status-${status}`;
            connectionStatusDiv.textContent = `连接状态：${message}`;
        }

        function displayLog(data) {
            const item = document.createElement('div');
            item.className = `log-item log-type-${data.type}`;
            const timestamp = new Date().toLocaleTimeString('zh-CN', { hour12: false });

            let content = '';
            if (data.type === 'vcp_log' && data.data) {
                content = `<strong>VCP 信息：</strong><br>${JSON.stringify(data.data, null, 2)}`;
            } else if (data.type === 'connection_ack') {
                content = `<strong>连接确认：</strong><br>${data.message}`;
            } else {
                content = `<strong>未知消息：</strong><br>${JSON.stringify(data, null, 2)}`;
            }
            item.innerHTML = `${content}<span class="log-timestamp"> (${timestamp})</span>`;
            
            // 将新消息添加到容器顶部
            logContainer.prepend(item);
            // 确保滚动条在顶部，以便查看最新消息
            logContainer.scrollTop = 0; 
        }

        function connectWebSocket() {
            if (ws && (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING)) {
                // 如果已经连接或正在连接，则不再创建新连接
                return;
            }

            updateStatus('connecting', '连接中...');
            logContainer.innerHTML = '<p>等待接收 VCP 日志信息...</p>'; // 清空并显示等待信息

            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                console.log('WebSocket Connection Opened:', event);
                updateStatus('open', '已连接！可以接收 VCP 信息啦！');
                displayLog({ type: 'connection_ack', message: 'VCPLog 连接成功！' });
            };

            ws.onmessage = function(event) {
                console.log('Received Message:', event.data);
                try {
                    const data = JSON.parse(event.data);
                    displayLog(data);
                } catch (e) {
                    console.error('Failed to parse message:', e);
                    displayLog({ type: 'error', data: `收到无法解析的消息: ${event.data.substring(0, 100)}...` });
                }
            };

            ws.onclose = function(event) {
                console.log('WebSocket Connection Closed:', event);
                updateStatus('closed', '连接已断开。尝试重连中...');
                // 尝试重连
                setTimeout(connectWebSocket, 3000); 
            };

            ws.onerror = function(error) {
                console.error('WebSocket Error:', error);
                updateStatus('error', '连接发生错误！请检查服务器或网络。');
                // 错误发生时也尝试重连，但不要过于频繁
                setTimeout(connectWebSocket, 5000); 
            };
        }

        function clearLogs() {
            logContainer.innerHTML = '<p>日志已清空。</p>';
        }

        // 页面加载时连接WebSocket
        window.onload = connectWebSocket;
    </script>
</body>
</html>
