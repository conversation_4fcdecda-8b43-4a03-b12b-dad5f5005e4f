### **VCP 服务器更新日志**

**2025-06-02**

1.  **优化日记渲染：** 提升了日记内容的显示效果，使其在界面上更加美观、易读。
2.  **新增日记创建推送功能：** 实现了日记内容的自动推送机制，确保重要信息能及时传达。
3.  **新增日记通知渲染：** 优化了日记通知的视觉呈现，让主人能更直观地接收到日记更新提醒。

**2025-06-01**

**主要更新：Agent提示词动态注入与角色封装**

*   **功能描述：** 引入了创新的Agent占位符机制，允许系统通过简洁的 `{{角色}}` 占位符（例如：`{{Xiaoke}}`），在运行时从独立的 `.txt` 文件动态加载并注入复杂的、多行的AI提示词。该机制实现了对Agent的完整角色卡（包括其身份、个性、背景信息）、关联知识库内容以及可用的VCP工具调用能力的封装与传递。

*   **核心优势：**
    *   **高度模块化：** 解决了传统环境变量（如 `agent.env`）在管理多行复杂文本时的局限性，实现了Agent配置与核心逻辑的有效分离。
    *   **增强灵活性：** 实现了复杂提示词的动态加载和精确注入，显著提升了系统配置的灵活性和可维护性。
    *   **效率提升：** 优化了Agent信息的管理与分发流程，提升了系统整体的运行效率。


**2025-05-31**
*   **版本升级**: 正式发布 VCP 2.0 版本。
*   **变量管理**: 引入 `Tar` 变量，构建 `Tar` → `Var` → `Sar` → `固定变量` 的逐层嵌套覆盖变量逻辑，提升信息注入的灵活性与精细度。
*   **核心重构**:
    *   `Plugin.js` 重构：集成完整的 VCP 调用追踪功能（发起 Agent、操作、结果接收方）。
    *   `Server.js` 重构：分离 WebSocket 处理逻辑至独立文件 `Websocket.js`，提高模块化程度。
    *   `Websocket.js` 重构：标准化协议设计，为未来拓展奠定基础。
*   **新功能**:
    *   **AgentMessage 插件**: 允许 Agent 向任意前端发送 WebSocket 消息，或通过外部 WebSocket 向 Agent 发送消息。
    *   兼容 Gotify 通知推送服务。
*   **文档**: 更新 `Readme` 文档，解释全新版本的使用。
*   **日志**: 更新 VCPLogs 插件，兼容新的调用追踪模式，并更新占位符，使 AI 可见所有已激活插件的调用信息。

**2025-05-30**
*   **新插件**:
    *   **AgentAssistant 插件**: 允许女仆 AI 之间互相通讯、委派任务和传递文件。
    *   **VCPLogs 插件**: 允许服务器推送 WebSocket 通知至任意前端，以获取 VCP 调用详情。
    *   **SillyTavern 通知栏插件**: 用于接收 VCPLogs 通知。
*   **修复**: 修复 AgentList 读取逻辑。

**2025-05-29**
*   **功能增强**: 天气插件新增小时预报和天气预警功能。
*   **兼容性**: 新增全套 SillyTavern 依赖外挂插件，实现 SillyTavern UI 布局、渲染和通讯端口与 VCP 高级功能的完全兼容。

**2025-05-28**
*   **修复**: 修复 SunoGen 音乐生成插件和 Tavily 搜索插件的部分函数格式问题。

**2025-05-27**
*   **新插件**:
    *   **UrlFetch 插件**: 允许 AI 获取指定 URL 的网页内容。
    *   **BilibiliFetch 插件**: 允许 AI 获取 Bilibili 视频或直播信息。

**2025-05-23**
*   **修复**: 修复之前更新导致的 Admin 管理面板插件开关丢失问题。

**2025-05-22**
*   **里程碑**: 正式上架 VCP 中文理论论文。

**2025-05-21**
*   **修复**: 修复 Admin 管理面板中的一个 iframe 子类关闭错误。

**2025-05-19**
*   **修复**: 修复服务器内部函数的大小写错误。
*   **重构**: 科学计算器插件重构，提升对复杂大型积分表达式的兼容性与鲁棒性。

**2025-05-18**
*   **新功能**:
    *   **模型列表获取**: 从前级服务器获取所有可用 AI 模型列表。
    *   **Base64 缓存管理器增强**: 优化管理面板中的 Base64 缓存管理器，包括：
        *   支持重新调用识图模型获取特定图片描述。
        *   支持批量获取图片描述。
        *   优化手动编辑布局。
        *   优化图片搜索和排序功能。

### 2025-05-17
*   **新功能:**
    *   管理面板新增功能完整的Base64缓存管理器。
    *   优化了表情包List逻辑。


### 2025-05-17
*   **新功能:**
    *   日历管理面板中的日记编辑小窗口现在支持缩放功能，优化了长文本的查看和编辑体验。
    *   引入了全新的全局变量体系：`Sar` 变量 (`{{SarXXX}}`)。与现有的 `Var` 变量不同，`Sar` 变量可以配置为仅对特定的 AI 模型 (ModelName) 生效，提供了更精细化和模型定制化的信息注入能力。
### 2025-05-16
*   **重构 & 改进:**
    *   **计算器插件重构:** 对科学计算器插件进行了全面重构，极大地增强了积分计算的**兼容性与鲁棒性**。现在插件能更稳定地处理各种复杂和非标准形式的积分表达式，确保能返回数值解或函数解，避免因公式异常而导致的错误
    *   **主服务文件重构:** 对 `Server.JS` 这个主服务文件进行了核心结构优化。将所有端口路由和请求分发相关的代码逻辑剥离，迁移到了新增的独立文件 `Routes.js` 中，显著提高了主文件的可读性和模块化程度。
    *   **依赖管理统一:** 统一了所有插件的依赖管理，现在项目的所有依赖都集中在主目录下进行管理，简化了项目的维护和部署流程。
### 2025-05-15
*   **新功能 & 改进:**
    *   **统一 SiliconFlow API 调用:** 将所有依赖 SiliconFlow 服务的插件（如 Wan2.1Video 和 FluxGen）的 API 调用逻辑进行了统一管理，现在它们都通过一个全局 API 进行调度，提升了效率和代码复用性。
    *   **天气插件数据源重构:** 天气插件的数据源已重构为更稳定可靠的和风天气 API，确保提供更准确、及时的天气信息。
    *   **管理面板-日记本增强:** 大幅改进了管理面板中的日记本管理功能。新增了日记内容的搜索、支持批量选择、多种排序选项（如按日期、修改时间）、以及在不同目录间移动日记文件的功能，极大地提升了日记管理的便捷性。
    *   **新增 AI 日记管理插件:** 添加了一个新的 VCP 插件，允许 AI 通过 VCP 工具调用该插件，主动对日记文件进行整理、编辑和移动等操作。这赋予了 AI 更高级的记忆管理能力！
### 2025-05-14
*   **新功能:**
    *   **新增服务器管理面板:** 引入了一个直观的 Web 管理面板。通过该面板，用户可以方便地查看和配置服务器的各项设置以及管理各种插件。
    *   **新增完整 Debug 模式:** 添加了一个完整的 Debug 模式开关。启用后，服务器几乎所有的功能调用和内部流程都会产生详细的日志输出，这为排查问题、理解运行状态和优化功能提供了极大的便利。
    *   **新增日记管理界面:** 构建了一个独立的日记管理界面，用户可以直接在此界面上查看、阅读和管理AI们记录的日常和重要信息。
