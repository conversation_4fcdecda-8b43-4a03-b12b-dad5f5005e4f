// Listen for messages from the background script
chrome.runtime.onMessage.addListener(handleMessages);

async function handleMessages(message) {
  if (message.target !== 'offscreen') {
    return;
  }

  if (message.type === 'DRAW_LABELS') {
    const { imageDataUrl, elements } = message.data;
    
    const canvas = new OffscreenCanvas(1, 1); // Initial size, will be resized
    const ctx = canvas.getContext('2d');

    const img = await createImageBitmapFromUrl(imageDataUrl);
    
    canvas.width = img.width;
    canvas.height = img.height;

    // Draw the original screenshot
    ctx.drawImage(img, 0, 0);

    // Draw labels on the canvas
    drawLabels(ctx, elements);

    // Get the new image as a Blob
    const blob = await canvas.convertToBlob({ type: 'image/png' });

    // Send the new image data URL back to the background script
    const reader = new FileReader();
    reader.onload = () => {
        chrome.runtime.sendMessage({
            type: 'DRAWING_COMPLETE',
            data: { labeledImageDataUrl: reader.result }
        });
    };
    reader.readAsDataURL(blob);
  }
}

function createImageBitmapFromUrl(url) {
    return new Promise((resolve, reject) => {
        fetch(url)
            .then(response => response.blob())
            .then(blob => createImageBitmap(blob))
            .then(resolve)
            .catch(reject);
    });
}

function drawLabels(ctx, elements) {
    elements.forEach(el => {
        const { x, y, width, height } = el.bounds;
        const label = el.label;

        // --- Draw the label background ---
        ctx.fillStyle = 'rgba(228, 28, 28, 0.9)'; // Strong red background
        const padding = 5;
        const fontSize = 16;
        ctx.font = `bold ${fontSize}px Arial`;
        const textMetrics = ctx.measureText(label);
        const textWidth = textMetrics.width;
        const bgWidth = textWidth + padding * 2;
        const bgHeight = fontSize + padding * 2;
        
        // Position at the top-left corner of the element
        let bgX = x;
        let bgY = y;

        // Prevent the label from going off-screen
        if (bgX < 0) bgX = 0;
        if (bgY < 0) bgY = 0;
        if (bgX + bgWidth > ctx.canvas.width) bgX = ctx.canvas.width - bgWidth;
        if (bgY + bgHeight > ctx.canvas.height) bgY = ctx.canvas.height - bgHeight;

        ctx.fillRect(bgX, bgY, bgWidth, bgHeight);

        // --- Draw the label text ---
        ctx.fillStyle = 'white';
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.fillText(label, bgX + padding, bgY + padding);

        // --- Draw a border around the element ---
        ctx.strokeStyle = 'rgba(228, 28, 28, 0.8)';
        ctx.lineWidth = 3;
        ctx.strokeRect(x, y, width, height);
    });
}
