console.log('VCPChrome background.js loaded.');
let ws = null;
let isConnected = false;
const defaultServerUrl = 'ws://localhost:8088';
const defaultVcpKey = 'your_secret_key';
const pendingWatchers = {}; // Object to hold pending watch requests and their timeouts

// --- WebSocket Management ---
function connect() {
    if (ws && ws.readyState === WebSocket.OPEN) return;
    chrome.storage.local.get(['serverUrl', 'vcpKey'], (result) => {
        const serverUrlToUse = result.serverUrl || defaultServerUrl;
        const keyToUse = result.vcpKey || defaultVcpKey;
        const fullUrl = `${serverUrlToUse}/vcp-chrome-observer/VCP_Key=${keyToUse}`;
        console.log('Connecting to:', fullUrl);

        ws = new WebSocket(fullUrl);
        ws.onopen = () => { isConnected = true; updateIcon(); broadcastStatusUpdate(); };
        ws.onmessage = handleWebSocketMessage;
        ws.onclose = () => { isConnected = false; ws = null; updateIcon(); broadcastStatusUpdate(); };
        ws.onerror = (error) => { console.error('WebSocket error:', error); isConnected = false; ws = null; updateIcon(); broadcastStatusUpdate(); };
    });
}

function disconnect() {
    if (ws) ws.close();
}

async function handleWebSocketMessage(event) {
    const message = JSON.parse(event.data);
    if (message.type !== 'command') return;

    const commandData = message.data;
    console.log('Received command:', commandData.command);

    if (commandData.command === 'watch_element') {
        handleWatchElement(commandData);
    } else if (commandData.command === 'cancel_watch') {
        handleCancelWatch(commandData);
    } else {
        switch (commandData.command) {
            case 'open_url': handleOpenUrl(commandData); break;
            case 'screenshot': handleScreenshot(commandData); break;
            case 'list_tabs': handleListTabs(commandData); break;
            case 'switch_tab': handleSwitchTab(commandData); break;
            case 'close_tab': handleCloseTab(commandData); break;
            case 'analyze_viewport': await handleAnalyzeViewport(commandData); break;
            default: forwardCommandToContentScript(commandData); break;
        }
    }
}

// --- Command Handlers ---
function handleWatchElement(commandData) {
    const { requestId, timeout = 30000 } = commandData;
    const timeoutId = setTimeout(() => {
        if (pendingWatchers[requestId]) {
            sendResponseToServer({ requestId, status: 'error', error: '观察超时。' });
            forwardCommandToContentScript({ command: 'cancel_watch', requestId });
            delete pendingWatchers[requestId];
        }
    }, timeout);
    pendingWatchers[requestId] = { timeoutId };
    forwardCommandToContentScript(commandData);
}

function handleCancelWatch(commandData) {
    const { requestId } = commandData;
    if (pendingWatchers[requestId]) {
        clearTimeout(pendingWatchers[requestId].timeoutId);
        delete pendingWatchers[requestId];
    }
    forwardCommandToContentScript(commandData);
}

function handleOpenUrl({ url, requestId, sourceClientId }) {
    let fullUrl = url.startsWith('http') ? url : `https://${url}`;
    chrome.tabs.create({ url: fullUrl }, (tab) => {
        if (chrome.runtime.lastError) sendResponseToServer({ requestId, status: 'error', error: `创建标签页失败: ${chrome.runtime.lastError.message}` });
        else sendResponseToServer({ requestId, sourceClientId, status: 'success', message: `成功打开URL: ${url}` });
    });
}

function handleScreenshot({ requestId, sourceClientId }) {
    chrome.tabs.captureVisibleTab(null, { format: 'png' }, (dataUrl) => {
        if (chrome.runtime.lastError) sendResponseToServer({ requestId, status: 'error', error: `截图失败: ${chrome.runtime.lastError.message}` });
        else sendResponseToServer({ requestId, sourceClientId, status: 'success', message: '成功捕获截图。', screenshot: dataUrl });
    });
}

function handleListTabs({ requestId, sourceClientId }) {
    chrome.tabs.query({ currentWindow: true }, (tabs) => {
        const tabList = tabs.map(({ id, title, url, active }) => ({ tabId: id, title, url, active }));
        sendResponseToServer({ requestId, sourceClientId, status: 'success', tabs: tabList });
    });
}

function handleSwitchTab({ tabId, requestId, sourceClientId }) {
    const id = parseInt(tabId, 10);
    if (isNaN(id)) return sendResponseToServer({ requestId, status: 'error', error: '无效的 tabId。' });
    chrome.tabs.update(id, { active: true }, (tab) => {
        if (chrome.runtime.lastError) sendResponseToServer({ requestId, status: 'error', error: `切换标签页失败: ${chrome.runtime.lastError.message}` });
        else sendResponseToServer({ requestId, sourceClientId, status: 'success', message: `成功切换到标签页 ID: ${id}` });
    });
}

function handleCloseTab({ tabId, requestId, sourceClientId }) {
    const id = parseInt(tabId, 10);
    if (isNaN(id)) return sendResponseToServer({ requestId, status: 'error', error: '无效的 tabId。' });
    chrome.tabs.remove(id, () => {
        if (chrome.runtime.lastError) sendResponseToServer({ requestId, status: 'error', error: `关闭标签页失败: ${chrome.runtime.lastError.message}` });
        else sendResponseToServer({ requestId, sourceClientId, status: 'success', message: `成功关闭标签页 ID: ${id}` });
    });
}

async function handleAnalyzeViewport(commandData) {
    const { requestId, sourceClientId } = commandData;
    try {
        const [screenshotDataUrl, elements] = await Promise.all([
            chrome.tabs.captureVisibleTab(null, { format: 'png' }),
            getActiveTab().then(tab => chrome.tabs.sendMessage(tab.id, { type: 'EXECUTE_COMMAND', data: { command: 'get_interactive_elements_info' } }))
        ]);
        if (!elements || elements.status !== 'success' || elements.elements.length === 0) {
            return sendResponseToServer({ requestId, sourceClientId, status: 'success', message: '当前视口未找到可交互元素。', screenshot: screenshotDataUrl, elements: {} });
        }
        await setupOffscreenDocument('offscreen.html', 'To draw labels on screenshots');
        const labeledImageDataUrl = await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('Offscreen document timed out.')), 5000);
            chrome.runtime.onMessage.addListener(function listener(message) {
                if (message.type === 'DRAWING_COMPLETE') {
                    clearTimeout(timeout);
                    chrome.runtime.onMessage.removeListener(listener);
                    resolve(message.data.labeledImageDataUrl);
                }
            });
            chrome.runtime.sendMessage({ type: 'DRAW_LABELS', target: 'offscreen', data: { imageDataUrl: screenshotDataUrl, elements: elements.elements } });
        });
        const elementMap = elements.elements.reduce((acc, el) => { acc[el.label] = { vcpId: el.vcpId, text: el.text }; return acc; }, {});
        sendResponseToServer({ requestId, sourceClientId, status: 'success', screenshot: labeledImageDataUrl, elements: elementMap });
    } catch (error) {
        console.error('Error during analyze_viewport:', error);
        sendResponseToServer({ requestId, status: 'error', error: error.message });
    }
}

// --- Offscreen Document & Utilities ---
let creating;
async function setupOffscreenDocument(path, reason) {
    const existingContexts = await chrome.runtime.getContexts({ contextTypes: ['OFFSCREEN_DOCUMENT'] });
    if (existingContexts.length > 0) return;
    if (creating) await creating;
    else {
        creating = chrome.offscreen.createDocument({ url: path, reasons: ['USER_MEDIA'], justification: reason });
        await creating;
        creating = null;
    }
}

function updateIcon() {
    const text = isConnected ? 'On' : 'Off';
    const color = isConnected ? '#00C853' : '#FF5252';
    chrome.action.setBadgeText({ text });
    chrome.action.setBadgeBackgroundColor({ color });
}

function sendResponseToServer(data) {
    if (ws && ws.readyState === WebSocket.OPEN) ws.send(JSON.stringify({ type: 'command_result', data }));
}

function sendEventToServer(eventType, eventData) {
    if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: eventType, data: eventData }));
    }
}

function forwardCommandToContentScript(commandData) {
    getActiveTab().then(tab => { if (tab) chrome.tabs.sendMessage(tab.id, { type: 'EXECUTE_COMMAND', data: commandData }); });
}

function broadcastStatusUpdate() {
    chrome.runtime.sendMessage({ type: 'STATUS_UPDATE', isConnected }).catch(e => {});
}

async function getActiveTab() {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    return tabs[0];
}

// --- Event Listeners ---
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'GET_STATUS') {
        sendResponse({ isConnected });
    } else if (request.type === 'TOGGLE_CONNECTION') {
        if (isConnected) disconnect(); else connect();
    } else if (request.type === 'PAGE_INFO_UPDATE') {
        sendEventToServer('pageInfoUpdate', { markdown: request.data.markdown });
    } else if (request.type === 'DIALOG_EVENT') {
        console.log('Background script forwarding dialog event:', request.data);
        sendEventToServer('dialogEvent', request.data);
    }
    else if (request.type === 'COMMAND_RESULT') {
        const { requestId } = request.data;
        if (pendingWatchers[requestId]) {
            clearTimeout(pendingWatchers[requestId].timeoutId);
            delete pendingWatchers[requestId];
        }
        sendResponseToServer(request.data);
    }
    return true;
});

chrome.tabs.onActivated.addListener((activeInfo) => {
    chrome.tabs.sendMessage(activeInfo.tabId, { type: 'REQUEST_PAGE_INFO_UPDATE' }).catch(e => {});
});

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'loading') chrome.tabs.sendMessage(tabId, { type: 'CLEAR_STATE' }).catch(e => {});
    if (changeInfo.status === 'complete' && tab.active) chrome.tabs.sendMessage(tabId, { type: 'REQUEST_PAGE_INFO_UPDATE' }).catch(e => {});
});

// Initialization
updateIcon();
