#!/usr/bin/env python3
"""
测试重构后的 MermaidGenerator 插件
"""
import json
import subprocess
import sys

def test_plugin(chart_code, description, expect_warning=False):
    """测试插件"""
    print(f"\n🧪 {description}")
    
    try:
        result = subprocess.run(
            [sys.executable, 'MermaidGenerator.py'],
            input=json.dumps({"chart_code": chart_code}),
            capture_output=True,
            text=True,
            cwd='Plugin/MermaidGenerator'
        )
        
        if result.returncode != 0:
            print(f"❌ 失败: {result.stderr}")
            return False
        
        # 检查警告
        has_warning = "检测到不安全的语法" in result.stderr
        if expect_warning and not has_warning:
            print("❌ 应该有警告但没有")
            return False
        elif not expect_warning and has_warning:
            print("❌ 不应该有警告但有了")
            return False
        
        if has_warning:
            print("⚠️ 已自动转换")
        
        # 检查输出
        output = json.loads(result.stdout)
        if output.get('status') == 'success':
            if '<img' in output.get('result', ''):
                print("✅ 成功")
                return True
            else:
                print("❌ 无图片")
                return False
        else:
            print(f"❌ 错误: {output.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def main():
    print("🚀 测试重构后的 MermaidGenerator 插件")
    
    tests = [
        # 危险语法（应该被转换）
        ("mindmap\n  root((主题))\n    理解与吸收", "思维导图语法", True),
        ("  理解与吸收\n  实践应用", "缩进结构", True),
        ("root((中心))\n  分支1", "root 语法", True),
        
        # 安全语法（不应该被转换）
        ("flowchart TD\n    A --> B", "标准流程图", False),
        ("graph TD\n    A --> B", "简单图表", False),
        ("A --> B\nB --> C", "节点连接", False),
        ("sequenceDiagram\n    A->>B: Hi", "序列图", False),
    ]
    
    passed = 0
    total = len(tests)
    
    for code, desc, expect_warn in tests:
        if test_plugin(code, desc, expect_warn):
            passed += 1
    
    print(f"\n📊 结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 重构成功！")
        print("\n✨ 新插件特点:")
        print("- 🏗️ 面向对象设计，代码更清晰")
        print("- 🛡️ 智能危险语法检测")
        print("- 🔄 自动安全转换")
        print("- ⚡ 更快的执行速度")
        print("- 🎯 精确的错误处理")
        print("- 📦 更小的代码体积")
        return True
    else:
        print("💥 测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
