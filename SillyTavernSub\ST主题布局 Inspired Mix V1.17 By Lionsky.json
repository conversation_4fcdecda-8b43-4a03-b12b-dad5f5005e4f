{"name": "Inspired Mod By Lion", "blur_strength": 0, "main_text_color": "rgba(24, 24, 27, 0.8)", "italics_text_color": "rgba(167, 167, 167, 1)", "underline_text_color": "rgba(188, 231, 207, 1)", "quote_text_color": "rgba(225, 138, 36, 1)", "blur_tint_color": "rgba(24, 24, 27, 1)", "chat_tint_color": "rgba(0, 0, 0, 0.3)", "user_mes_blur_tint_color": "rgba(0, 0, 0, 0.9)", "bot_mes_blur_tint_color": "rgba(0, 0, 0, 0.9)", "shadow_color": "rgba(209, 213, 219, 1)", "shadow_width": 5, "border_color": "rgba(31, 31, 34, 1)", "font_scale": 1.12, "fast_ui_mode": false, "waifuMode": false, "avatar_style": 0, "chat_display": 0, "noShadows": false, "chat_width": 78, "timer_enabled": true, "timestamps_enabled": false, "timestamp_model_icon": false, "mesIDDisplay_enabled": false, "hideChatAvatars_enabled": false, "message_token_count_enabled": false, "expand_message_actions": true, "enableZenSliders": false, "enableLabMode": false, "hotswap_enabled": false, "custom_css": "body {\n  display: flex;\n  --nav-bar-width: 64px;\n  --char-panel-width: 0px;\n  --settings-panel-width: 0px;\n  --primaryBlue: rgb(88, 101, 242);\n  --hoverPrimary: var(--black30a);\n  --selected: var(--grey5050a);\n}\n\n\n#send_form {\n  background: transparent; /* 修复输入栏后的黑色背景 */\n  border: none ;\n  outline-style: none !important;\n  margin-left: -24px;\n}\n\n#nonQRFormItems {\n  padding: 8px;\n  align-items:flex-end;\n  background-color:#202024;\n  border-color:#303136;\n  border-radius:28px;\n  border-style:solid;\n  border-width:0.8px;\n  color:#fafafa;\n  display:flex;\n  line-height:24px;\n  margin:16px;\n  padding:4px;\n  align-items:center;\n}\n#chat>.mes {\n  max-width: 80rem;\n}\n.mes:hover {\n  background: #00000000 !important;\n}\n.mes_text {\n  background-color: rgba(36,37,41, 0.8); /* 修改为半透明背景 */\n  border-radius:16px;\n  border-width:1px;\n  display: flex;\n  flex-direction: column;\n  place-items: center;\n  line-height:24px;\n  margin:4px 0px 0px;\n  padding:12px  !important;\n  max-width: 956px;\n  width: fit-content;\n  align-items: flex-start;\n  backdrop-filter: blur(var(--SmartThemeBlurStrength)); /* 添加磨砂效果 */\n  -webkit-backdrop-filter: blur(var(--SmartThemeBlurStrength)); /* 兼容性前缀 */\n}\n.mes_text p {\n  color-scheme: dark;\n  color: #D1D5DBD4;\n  margin-bottom: 0;\n}\n\n.mes[is_user=\"true\"] {\n  flex-direction: row-reverse;\n}\n.mes[is_user=\"true\"] .mes_text {\n  margin-right: 0;\n  margin-left: auto;\n}\n\n.mes[is_user=\"true\"] .flex-container{\n    flex-direction: row-reverse;\n}\n.mes[is_user=\"true\"] .name_text{\n    padding-right: 10px;\n}\n\n.last_mes .swipe_left{\n  position: absolute;\n  bottom: -36px;\n  left: 36px;\n}\n\n.last_mes .swipeRightBlock {\n  position: absolute;\n  display: flex;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n  bottom: -36px;\n  left: 56px;\n}\n\n.last_mes .fa-chevron-left{\n  transform: scale(0.7);\n}\n\n#send_form .fa-bars{\n  transform: scale(0.7);\n}\n\n#send_form .fa-magic-wand-sparkles{\n  transform: scale(0.7);\n}\n\n#send_form .fa-paper-plane{\n  transform: scale(0.7);\n}\n\n.last_mes .fa-chevron-right {\n  margin-left: -6px;\n  transform: scale(0.7);\n}\n.name_text {\n  margin-bottom: 4px;\n  margin-left: 10px;\n  color: rgb(250, 250, 250);\n  font-weight: 400;\n  font-size: .75rem;\n  line-height: 1rem;\n}\n\nbody:has(#right-nav-panel.openDrawer) {\n  --char-panel-width: 360px;\n}\n\nbody:has(#left-nav-panel.openDrawer) {\n  --settings-panel-width: 340px;\n}\n\n#top-bar {\n  border-bottom: unset;\n}\n\n#top-bar,\n#top-settings-holder {\n  margin: unset;\n  width: unset;\n  height: unset;\n  position: unset;\n}\n\n#top-settings-holder {\n  background-color: var(--tertiaryBg);\n  height: 100vh;\n  height: 100dvh;\n  flex-direction: column;\n  padding: 16px 8px;\n  gap: 16px;\n  justify-content: unset;\n  width: var(--nav-bar-width);\n\n  &::before {\n    content: \"\";\n    width: 100%;\n    aspect-ratio: 1/1;\n    background-image: url(\"img/five.png\");\n    background-size: cover;\n    background-position: center center;\n    border-radius: 50%;\n  }\n}\n\n.drawer-icon {\n  font-size: 1.5em;\n  background-color: var(--secondaryBg);\n  width: 48px;\n  height: 48px;\n  line-height: 48px;\n  text-align: center;\n  border-radius: 100%;\n  &.openIcon {\n    background-color: var(--primaryBlue);\n    border-radius: 40%;\n  }\n}\n\n/* .drawer-icon {\n  font-size: 1.5em;\n} */\n/* Commented out duplicate rule */\n\n#sys-settings-button:not(:has(.online_status_indicator.success)) .drawer-icon,\n#user-settings-button .drawer-icon {\n  &:before {\n    margin-left: 4px;\n    transition: none;\n  }\n}\n\n#right-nav-panel {\n  left: var(--nav-bar-width);\n  width: var(--char-panel-width);\n  height: 100vh;\n  height: 100dvh;\n  z-index: 10 !important;\n  max-height: unset;\n  border-radius: unset;\n  border: unset;\n  min-width: unset;\n  background-color: var(--secondaryBg);\n}\n\n#character_name_pole {\n  position: absolute;\n  top: 12%;\n  width: 77%;\n}\n\nbody.charListGrid #rm_print_characters_block {\n  flex-direction: row;\n  justify-content: flex-start; /* Align items to the start */\n  flex-wrap: wrap;\n  gap: 12px; /* Gap between cards */\n  padding: 5px 8px;\n\n  & .group_select,\n  & .character_select,\n  & .bogus_folder_select {\n    width: calc((100% - 24px) / 3); /* For 3 columns with 12px gap (2 gaps) */\n    flex-direction: column; /* Stack avatar and name vertically */\n    align-items: center; /* Center avatar and name container horizontally */\n    overflow: unset;\n    max-width: unset;\n    padding-bottom: 10px; /* Space below each card */\n\n    &:hover {\n      background-color: var(--hoverPrimary);\n\n      &.is_fav::before {\n        outline-color: var(--hoverPrimary);\n      }\n    }\n\n    &.is_fav,\n    &.is_fav {\n      position: relative;\n\n      &::before {\n        content: \"\";\n        position: absolute;\n        z-index: 9999;\n        left: 27px;\n        bottom: 5px;\n        width: 10px;\n        height: 10px;\n        border-radius: 100%;\n        background-color: var(--active);\n        outline: var(--secondaryBg) solid 2px;\n      }\n\n      & .avatar {\n        outline: unset;\n      }\n\n      & .ch_name {\n        color: unset;\n      }\n    }\n\n    & .ch_name {\n      text-align: center; /* Center the name text below avatar */\n      font-size: var(--mainFontSize);\n      max-width: 100%; /* Ensure name can take full width of its container */\n      word-break: break-word; /* Allow long names to wrap */\n    }\n\n    & .group_name_block,\n    & .character_select_container {\n      width: 100%; /* Name container takes full card width */\n      margin-top: 6px; /* Space between avatar and name text block */\n      /* display: flex and justify-content: center removed for simplicity, */\n      /* .ch_name text-align:center will handle centering text */\n    }\n  }\n}\n\n.bogus_folder_select {\n  & .avatar {\n    font-size: 12px;\n    outline: none;\n  }\n\n  & .bogus_folder_icon {\n    margin-left: 1px;\n\n    &.fa-right-from-bracket {\n      margin-left: -3px;\n      margin-top: -1px;\n    }\n  }\n}\n\n.character_select {\n  border-radius: 4px;\n}\n\n#rm_characters_block {\n  overflow-x: hidden;\n}\n\n.avatar { /* General avatar style, PC might use this */\n  width: 65px;\n  height: 65px;\n  border-radius: 50%; /* Ensure avatars are circular */\n\n  & img {\n    width: 100%;\n    height: 100%;\n    border-radius: 50%; /* Ensure images within are circular */\n  }\n}\n\nbody:not(.big-avatars) .avatar_collage {\n  min-width: 32px;\n}\n\n#rm_print_characters_pagination {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr); /* 改为2列 */\n  grid-auto-rows: auto; /* 允许行高自动适应内容 */\n  position: relative;\n \n  &::after {\n    content: \"Characters\";\n    text-transform: uppercase;\n    width: 100%;\n    height: 80%;\n    grid-column: 1 / span 2; /* 适应2列布局 */\n    grid-row: 4; /* 假设图标会占据前3行，此伪元素仍在第4行 */\n    font-size: calc(var(--mainFontSize) * 0.8);\n    display: flex;\n    padding-left: 13px;\n    align-items: end;\n    font-weight: bold;\n    letter-spacing: 0.145ch;\n  }\n}\n\n.paginationjs {\n  grid-row: 3;\n  margin: 4px -3px 8px 8px;\n  flex-direction: row-reverse;\n  justify-content: space-between;\n  margin-top: -4px;\n}\n\n.paginationjs-pages ul li.disabled a {\n  border: none;\n  opacity: 0.75;\n}\n\n.paginationjs-pages ul li.disabled {\n  cursor: not-allowed;\n}\n\n.paginationjs-pages ul li a {\n  border: none;\n}\n\nselect,\n.paginationjs-pages ul li,\n.menu_button {\n  height: 29px;\n  transition: 0s;\n  background-color: var(--tertiaryBg);\n  border-radius: 4px;\n  padding: 4px;\n  border: none;\n}\n\n.paginationjs-nav {\n  bottom: 0;\n  right: 8px;\n  position: absolute;\n  padding-bottom: 2px;\n}\n\n#charListGridToggle {\n  display: none;\n}\n\n#bulkDeleteButton::after {\n  content: \"Bulk Delete\";\n}\n\n#bulkEditButton {\n  grid-row: 1;\n\n  &::after {\n    content: \"Bulk Edit\";\n  }\n\n  &:hover {\n    background-color: var(--hoverPrimary);\n  }\n\n  &.bulk_edit_overlay_active {\n    background-color: var(--selected);\n  }\n}\n\n#bulkDeleteButton {\n  grid-row: 2;\n  margin-bottom: 4px !important;\n\n  &:hover {\n    background-color: var(--hoverPrimary);\n    color: var(--warning);\n  }\n}\n\n#bulkEditButton,\n#bulkDeleteButton {\n  background: transparent;\n  border: none;\n  margin-left: 8px;\n  margin-right: 8px;\n  margin-bottom: 0;\n  padding: 5px;\n  font-size: 18px;\n  justify-content: unset;\n  width: calc(100% - 8px);\n\n  &::before {\n    margin: 4px;\n  }\n\n  &::after {\n    font-size: calc(var(--mainFontSize) * 0.8);\n    font-family: \"Noto Sans\", \"Noto Color Emoji\", sans-serif;\n    text-transform: unset;\n    padding-left: 7px;\n    font-weight: bold;\n    letter-spacing: 0.145ch;\n  }\n}\n\n#character_search_bar {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  width: 80%;\n}\n\nhr {\n  display: none;\n}\n\n#HotSwapWrapper {\n  display: none;\n}\n\n#CharListButtonAndHotSwaps > .flexFlowColumn {\n  flex-direction: column-reverse;\n  margin-left: 6px;\n  margin-top: -1px;\n}\n\n#rm_button_characters {\n  display: none;\n  margin-top: 2px;\n\n  &::before {\n    content: \"\\f060\";\n  }\n}\n\n#rm_PinAndTabs {\n  margin-top: 4px;\n\n  &:has(~ #rm_characters_block[style*=\"display: none;\"])\n    #rm_button_selected_ch {\n    display: flex;\n    justify-content: right;\n    width: 80%;\n    top: 6px;\n    right: 8px;\n  }\n}\n\n#CharListButtonAndHotSwaps:has(\n    ~ #rm_characters_block[style*=\"display: none;\"]\n  ) {\n  & #rm_button_characters {\n    display: unset;\n  }\n\n  & #rm_button_panel_pin_div {\n    display: none;\n  }\n}\n\n#rm_button_selected_ch {\n  position: absolute;\n  right: 10000px;\n  opacity: 1;\n}\n\n#avatar_load_preview {\n  width: 48px;\n  height: 32px;\n}\n\n.showTagList {\n  margin-right: 0;\n}\n\n.tag {\n  /* 移除了 grid-row 和 grid-column，以便图标可以自由流动 */\n \n  &:nth-child(5) {\n    margin-left: 2px;\n  }\n\n  &.actionable {\n    background: transparent !important;\n    opacity: 1;\n    border: none;\n    border-radius: 4px;\n    aspect-ratio: unset;\n    width: auto; /* 允许项目适应网格单元格宽度 */\n    height: 29px;\n    display: flex;\n    align-items: center;\n    margin-bottom: 8px;\n    filter: unset;\n    justify-content: left;\n    padding-left: 8px;\n\n    &:hover {\n      background-color: var(--hoverPrimary) !important;\n    }\n\n    &.selected {\n      background-color: var(--selected) !important;\n    }\n\n    & .tag_name {\n      height: 29px;\n      line-height: 29px;\n      display: flex;\n      opacity: 1;\n\n      &::before {\n        height: 18px;\n        width: 18px;\n        font-size: 18px;\n        display: block;\n      }\n\n      &::after {\n        font-size: calc(var(--mainFontSize) * 0.8);\n        font-family: \"Noto Sans\", \"Noto Color Emoji\", sans-serif;\n        text-transform: unset;\n        padding-left: 14px;\n        font-weight: bold;\n        letter-spacing: 0.145ch;\n      }\n    }\n  }\n}\n\n.filterByGroups .tag_name::after {\n  content: \"Groups\";\n}\n\n.manageTags .tag_name::after {\n  content: \"Manage Tags\";\n}\n\n.rm_tag_filter {\n  padding: 0 2px;\n}\n\n.showTagList {\n  &:not(.selected) {\n    margin-bottom: 0 !important;\n  }\n\n  & .tag_name::after {\n    content: \"Tags\";\n  }\n}\n\n.filterByFavorites {\n  margin-top: 8px;\n  color: var(--golden);\n\n  & .tag_name::after {\n    content: \"Favorites\";\n  }\n}\n\n#avatar_div_div {\n  position: absolute;\n  top: 52px;\n  left: 20px;\n  width: 56px;\n  height: 56px;\n\n  & #avatar_load_preview {\n    width: 100%;\n    height: 100%;\n  }\n}\n\n.form_create_bottom_buttons_block {\n  width: 70%;\n  margin-left: auto;\n}\n\n#result_info {\n  justify-content: right;\n  width: 100%;\n}\n\n.text_pole {\n  border: none;\n}\n\n#first_message_div {\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: start;\n  gap: 6px;\n}\n\ntextarea {\n  border: none;\n}\n\n#sheld {\n  left: calc(var(--nav-bar-width) + var(--char-panel-width)) !important;\n  justify-content: left;\n  align-items: center;\n  margin: unset;\n  height: 100vh;\n  height: 100dvh;\n  max-height: unset;\n  top: 0;\n  width: calc(\n    100vw - var(--nav-bar-width) - var(--char-panel-width) -\n      var(--settings-panel-width)\n  );\n  width: calc(\n    100dvw - var(--nav-bar-width) - var(--char-panel-width) -\n      var(--settings-panel-width)\n  );\n  /* Original background, commented out for testing */\n  /* background: rgba(var(--primaryBgCol), var(--primaryBgAlpha)); */\n  background: transparent !important; /* Test: Force transparent background for #sheld */\n  backdrop-filter: blur(var(--SmartThemeBlurStrength));\n  -webkit-backdrop-filter: blur(var(--SmartThemeBlurStrength));\n\n  &:has(~ #expression-wrapper #expression-image:not([src=\"\"])) {\n    & > #chat,\n    & > #form_sheld {\n      width: calc(\n        100vw - var(--nav-bar-width) - var(--char-panel-width) -\n          var(--settings-panel-width) - ((100vw - var(--sheldWidth)) / 2)\n      );\n    }\n  }\n}\n\n#chat,\n#form_sheld {\n  width: calc(\n    100vw - var(--nav-bar-width) - var(--char-panel-width) -\n      var(--settings-panel-width)\n  );\n}\n\n#chat {\n  max-height: unset;\n  background: transparent;\n  padding-top: 24px;\n  padding-left: 8px;\n  padding-bottom: 12px;\n  backdrop-filter: none;\n  align-items: center;\n}\n\n#form_sheld {\n  max-width: 656px;\n  padding: 8px 16px;\n  background-color: transparent !important;\n}\n\n#left-nav-panel {\n  right: 0;\n  left: unset;\n  width: var(--settings-panel-width);\n  border: none;\n  border-radius: unset;\n  height: 100vh;\n  height: 100dvh;\n  max-height: unset;\n  background: var(--secondaryBg);\n}\n\n#clickSlidersTips {\n  display: none !important;\n}\n\n#lm_button_panel_pin_div {\n  margin-left: 8px;\n}\n\n.note-link-span.topRightInset[name=\"samplerHelpButton\"] {\n  top: 8px;\n  right: 6px;\n}\n\n.neo-range-input {\n  border: none;\n  background: var(--tertiaryBg);\n  margin: 8px 0;\n  padding: 4px;\n  border-radius: 4px;\n}\n\n.neo-range-slider {\n  background: var(--tertiaryBg) !important;\n  border-radius: 4px !important;\n  height: 8px !important;\n}\n\ninput[type=\"number\"] {\n  -moz-appearance: textfield;\n  border: none;\n  border-radius: 4px;\n  height: 29px;\n}\n\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n  -webkit-appearance: none;\n}\n\ninput[type=\"range\"] {\n  -webkit-appearance: none;\n  background: var(--primaryBlue) !important;\n  border-radius: 4px !important;\n  height: 8px !important;\n}\n\ninput[type=\"range\"]::-webkit-slider-thumb {\n  -webkit-appearance: none;\n  height: 18px;\n  width: 6px;\n  border-radius: 3px;\n  border: 1.5px solid gray;\n  box-shadow: none;\n  box-sizing: content-box;\n}\n\ninput[type=\"range\"]::-moz-range-thumb {\n  height: 18px;\n  width: 6px;\n  border-radius: 3px;\n  border: 1.5px solid gray;\n  box-shadow: none;\n  box-sizing: content-box;\n}\n\ninput[type=\"checkbox\"] {\n  margin: 0;\n  padding: 0;\n  border: none !important;\n  background: var(--tertiaryBg) !important;\n  border-radius: 100% !important;\n  outline: none !important;\n  box-shadow: none !important;\n\n  &:checked {\n    background: #22a559 !important;\n\n    &::before {\n      content: \"\\f00c\" !important;\n      font-family: \"Font Awesome 6 Free\";\n      background: #22a559 !important;\n      color: white !important;\n      clip-path: unset !important;\n      transition: unset !important;\n      height: unset !important;\n      width: unset !important;\n      transform: unset !important;\n      box-shadow: unset !important;\n      font-weight: bold;\n      font-size: 0.6em;\n    }\n  }\n}\n\n#anchor_checkbox label,\n#power-user-option-checkboxes label,\n.checkbox_label,\n.auto_continue_settings_block {\n  align-items: center;\n}\n\n.prompt_order > div {\n  border: none;\n  height: 29px;\n  display: flex;\n  align-items: center;\n  padding: 5px 10px 5px 25px;\n\n  &::after {\n    height: 10px;\n    line-height: 7px;\n  }\n}\n\n.mesAvatarWrapper .avatar { /* This is the general rule for avatar in messages */\n  width: 65px; /* Default size, might be overridden by mobile */\n  height: 65px;\n  border-radius: 50%;\n}\n\n.mes {\n  border-radius: 8px;\n  position: relative;\n  width: calc(100% - 8px);\n\n  &:hover {\n    background: var(--hoverPrimary);\n\n    & .mes_buttons {\n      display: flex !important;\n    }\n  }\n\n  & .mes_buttons {\n    display: none !important;\n    position: absolute;\n    top: -12px;\n    right: 24px;\n    background: var(--secondaryBg);\n    outline: 1px solid var(--tertiaryBg);\n    padding: 4px;\n    border-radius: 3px;\n  }\n}\n\n.mes_buttons .mes_edit,\n.mes_buttons .mes_bookmark,\n.mes_buttons .mes_create_bookmark,\n.extraMesButtonsHint,\n.tagListHint,\n.extraMesButtons div {\n  color: rgb(181, 186, 193);\n  opacity: 1;\n}\n\n.zoomed_avatar_img {\n  display: none;\n}\n\nbody * {\n  text-shadow: 0px 0px calc(var(--shadowWidth) * 1px)\n    var(--SmartThemeChatTintColor) !important;\n}\n\n.drawer-content {\n  width: calc(100vw - var(--nav-bar-width));\n  width: calc(100dvw - var(--nav-bar-width));\n  height: 100vh;\n  height: 100dvh;\n  max-height: unset;\n  min-width: unset;\n  border: none;\n  border-radius: unset;\n  left: var(--nav-bar-width);\n  margin: 0;\n  z-index: 20 !important;\n  box-shadow: none;\n  top: 0;\n  background: rgb(var(--primaryBgCol));\n  padding: 16px;\n  padding-top: calc(var(--nav-bar-width) + env(safe-area-inset-top) + 16px); /* 确保内容在导航栏下方开始，并保留原有padding */\n  box-sizing: border-box; /* 确保 padding 不会增加总高度 */\n}\n\n#extensions_settings .inline-drawer-toggle.inline-drawer-header,\n#extensions_settings2 .inline-drawer-toggle.inline-drawer-header,\n#user-settings-block h4,\n.standoutHeader {\n  background: transparent;\n  border: none;\n  font-size: 1.2em;\n}\n\n.select2-container .select2-selection--multiple,\n.select2-container .select2-selection--single,\n.select2-container.select2-container--focus .select2-selection--multiple,\n.select2-container.select2-container--focus .select2-selection--single {\n  border: none;\n}\n\nbody:has(.online_status_indicator.success) #sys-settings-button .drawer-icon {\n  background: green !important;\n}\n\n#sys-settings-button .drawer-icon {\n  background: red !important;\n}\n\n.redOverlayGlow {\n  color: unset;\n}\n\n.drawer-icon.openIcon {\n  transition: none;\n}\n\n.drag-grabber {\n  display: none !important;\n}\n\n.select2-container--default\n  .select2-selection--multiple\n  .select2-selection__choice {\n  border: none;\n}\n\n.select2-container .select2-selection--multiple .select2-selection__choice,\n.select2-container .select2-selection--single .select2-selection__choice {\n  border: none;\n}\n\n.select2-container\n  .select2-selection--multiple\n  .select2-selection__choice__remove {\n  border-right: none;\n}\n\n#select2-world_info-container > li {\n  padding: 2px;\n\n  & > button {\n    height: 100%;\n    width: 16px;\n\n    span {\n      height: 10px;\n      display: block;\n      width: 10px;\n      line-height: 8px;\n    }\n  }\n\n  & > span {\n    margin-left: 12px;\n  }\n}\n\n.bg_list {\n  width: unset;\n}\n\n.drawer-icon.closedIcon {\n  opacity: 0.8;\n  transition: none;\n}\n\n#expression-wrapper {\n  z-index: 30 !important;\n  width: 0 !important;\n  height: 0 !important;\n}\n\n#expression-image {\n  mask-image: linear-gradient(270deg, black 95%, transparent 100%);\n}\n\n#expression-holder {\n  right: var(--settings-panel-width);\n  left: unset;\n}\n\n#quickReplyBarPopout {\n  z-index: 9999;\n}\n\n.quickReplyButton {\n  margin: 0 !important;\n  border: none !important;\n  padding: 4px !important;\n  border-radius: 4px !important;\n}\n\n#quickReplies {\n  justify-content: left !important;\n  margin-bottom: 4px !important;\n}\n\n#quickReplyPopoutButton {\n  display: none;\n}\n\n.hotswapAvatar img,\n.avatar img { /* Ensures images inside .avatar are also rounded if .avatar is */\n  border: none;\n  border-radius: inherit; /* Inherit border-radius from parent .avatar */\n}\n\n.add_avatar:hover {\n  filter: none;\n}\n\ninput[type=\"checkbox\"]:not(#nav-toggle):not(#rm_button_panel_pin):not(\n    #lm_button_panel_pin\n  ):not(#WI_panel_pin) {\n  filter: none;\n}\n\n#rm_print_characters_block.group_overlay_mode_select\n  .character_select.character_selected {\n  background-color: var(--selected);\n}\n\n.fa-solid::before {\n  text-shadow: none !important;\n}\n\n#floatingPrompt {\n  z-index: 40 !important;\n  right: var(--settings-panel-width);\n  left: unset;\n  width: ((100vw - var(--sheldWidth)) / 2);\n  border-radius: 0;\n  height: 100vh;\n  height: 100dvh;\n  max-height: unset;\n  box-shadow: none;\n}\n\n#completion_prompt_manager #completion_prompt_manager_list {\n  overflow-y: scroll;\n}\n\n.mes_block {\n  padding-left: 0;\n  transform: none !important;\n}\n\n.mes.lastInContext {\n  border-top: none !important;\n  margin-top: 2em;\n  &:before {\n    content: \"\";\n    position: absolute;\n    right: 0;\n    bottom: calc(100% + 0.4em);\n    border-bottom: 2px solid var(--preferred);\n    padding-left: 100%;\n  }\n  &:hover::after {\n    display: none;\n  }\n\n  &:after {\n    --arrow: 10px;\n    content: \"Context\";\n    position: absolute;\n    bottom: calc(100% - 0.4em);\n    right: 0;\n    background: var(--preferred);\n    line-height: 1.5em;\n    padding: 0.125em 0.5em 0.125em calc(0.2em + var(--arrow));\n    clip-path: polygon(\n      0 50%,\n      var(--arrow) 100%,\n      100% 100%,\n      100% 0%,\n      var(--arrow) 0%\n    );\n    border-radius: 5px;\n  }\n}\n\n/* Move lorebook pagination info to the top */\n#world_info_pagination .paginationjs-nav {\n  bottom: unset;\n}\n\n/* fix worldinfo control row being unaligned */\n#world_info_pagination .paginationjs {\n  margin: 0;\n}\nbody {\n  #user-settings-block-content {\n    > :nth-child(1),\n    > :nth-child(3) {\n      flex: 0 0 25%;\n    }\n    > :nth-child(2) {\n      display: flex;\n      flex-direction: column;\n      flex-wrap: wrap;\n      > :nth-child(4) {\n        display: contents;\n      }\n      #CustomCSS-block {\n        flex-basis: 100%;\n        margin-left: 5px;\n        > .flex-container {\n          flex: 1 1 auto;\n          align-items: stretch;\n          > #customCSS {\n            height: unset;\n            white-space: pre;\n          }\n        }\n      }\n    }\n  }\n}\nbody #vv--root {\n  width: 360px;\n  right: unset;\n  left: var(--nav-bar-width);\n  max-height: 100svh;\n  z-index: 2000 !important;\n}\nbody:has(#vv--root) {\n  --char-panel-width: 360px;\n}\nbody {\n  --primaryBgCol: var(--SmartThemeCheckboxBgColorR),\n    var(--SmartThemeCheckboxBgColorG), var(--SmartThemeCheckboxBgColorB);\n  --primaryBgAlpha: var(--SmartThemeCheckboxBgColorA);\n  --SmartThemeBodyColor: var(--SmartThemeShadowColor);\n  --secondaryBg: var(--SmartThemeBlurTintColor);\n  --tertiaryBg: var(--SmartThemeBorderColor);\n}\n\ndiv:has(> span[data-i18n=\"Main Text\"]) {\n  & > span {\n    display: none;\n  }\n\n  &::after {\n    color: var(--textColor);\n    display: inline;\n    content: \"Chat Background\";\n  }\n}\n\ndiv:has(> span[data-i18n=\"Shadow Color\"]) {\n  & > span {\n    display: none;\n  }\n\n  &::after {\n    color: var(--textColor);\n    display: inline;\n    content: \"Main Text\";\n  }\n}\n\ndiv:has(> span[data-i18n=\"Chat Background\"]) {\n  & > span {\n    display: none;\n  }\n\n  &::after {\n    color: var(--textColor);\n    display: inline;\n    content: \"Text Shadow\";\n  }\n}\n\n/* Additions for Responsive Design */\n@media (max-width: 768px) { /* 移动设备特定样式 Mobile-specific styles */\n    body {\n        padding-top: calc(48px + env(safe-area-inset-top)); /* 使用实际的顶栏高度 */\n        padding-bottom: env(safe-area-inset-bottom);\n        padding-left: 0;\n        padding-right: 0;\n        overflow-x: hidden;\n    }\n\n    /* 导航栏 - 固定在顶部 */\n    #top-settings-holder {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        width: 100%;\n        height: 48px; /* 减小手机版顶栏高度 */\n        flex-direction: row;\n        justify-content: center; /* 将 justify-content 改为 center */\n        align-items: center;\n        background-color: var(--secondaryBg, #1A1A1D);\n        border-bottom: 1px solid var(--tertiaryBg, #303136);\n        padding: 0; /* 移除水平内边距 */\n        padding-top: env(safe-area-inset-top);\n        z-index: 1000; /* Ensure nav bar is above general content but below opened panels if they are meant to overlay temporarily */\n        gap: 4px; /* Reduced gap between icon containers */\n        box-sizing: border-box;\n    }\n\n    #top-settings-holder::before {\n        display: none;\n    }\n\n    #top-settings-holder > * {\n        flex-shrink: 1; \n        min-width: 0; \n        flex-basis: auto; \n        display: flex; \n        justify-content: center;\n        align-items: center;\n    }\n\n    #top-settings-holder .drawer-icon {\n        font-size: 1.2em;\n        width: 36px;\n        height: 36px;\n        line-height: 36px;\n        position: relative; /* Required for z-index to apply */\n        z-index: 1001;     /* Ensure icons are above panels */\n    }\n\n    #sheld {\n        position: relative !important;\n        top: unset !important;\n        left: 0 !important;\n        width: 100% !important;\n        height: auto !important;\n        margin-left: 0;\n        padding-top: 0;\n        min-height: calc(100dvh - (48px + env(safe-area-inset-top))); /* 使用 dvh 调整最小高度以适应新的顶栏高度 */\n        box-sizing: border-box;\n    }\n\n    #chat {\n        width: 100%;\n        max-width: 100%;\n        padding-left: 8px; \n        padding-right: 8px;\n        padding-top: 16px;\n        box-sizing: border-box;\n    }\n\n    #form_sheld { /* Container for the input bar items */\n        width: 100% !important; /* 强制占据全部宽度 */\n        padding: 2px 1px !important; /* 调整垂直和水平 padding，并提高优先级 */\n        margin: 0;\n        box-sizing: border-box;\n        background: transparent; /* 尝试移除输入栏后方可能的黑色背景 */\n        border: none !important; /* 彻底移除 form_sheld 的边框 */\n    }\n\n    #send_form { /* The form itself, parent of #nonQRFormItems */\n        margin: 0 auto; /* 使其居中 */\n        display: flex; /* Ensure it's a flex container if it wraps nonQRFormItems */\n        width: 95%;\n        border: none !important; /* 彻底移除 send_form 的边框 */\n    }\n\n    #nonQRFormItems { /* This is the main bar with icons and textarea */\n        margin: 8px 0; /* 移除水平 margin */\n        width: 100%; /* Make it take full available width within #form_sheld padding */\n        padding: 2px 8px; /* Inner padding of the input bar */\n        display: flex; /* 确保是 flex 容器 */\n        flex-direction: row; /* Ensure items are in a row */\n        align-items: center; /* Vertically center items in the input bar */\n        gap: 4px; /* 减小元素间间距 */\n        box-sizing: border-box; /* 确保 padding 和 border 不会增加总宽度 */\n        border: none !important; /* 再次强调移除所有边框 */\n        box-shadow: none !important; /* 移除可能的阴影 */\n        flex-wrap: nowrap; /* 强制子元素不换行 */\n    }\n\n    /* 假设 #nonQRFormItems 的第一个直接子元素是包裹左侧图标的容器 */\n    #nonQRFormItems > div:first-child {\n        display: flex !important; /* 使用 !important 提高优先级 */\n        flex-direction: row !important;\n        align-items: center !important;\n        gap: 4px !important; /* 图标之间的间距 */\n        flex-shrink: 0; /* 防止这个容器被压缩 */\n        flex-wrap: nowrap !important; /* 强制子元素不换行 */\n    }\n\n    /* 确保图标按钮（无论是否在div内）本身不会导致竖排 */\n    #nonQRFormItems .menu_button,\n    #nonQRFormItems button,\n    #nonQRFormItems > a { /* 也考虑图标可能在 a 标签内 */\n        display: inline-flex !important; /* 强制行内弹性布局 */\n        align-items: center !important;\n        justify-content: center !important;\n        flex-shrink: 0 !important; /* 防止图标按钮被压缩换行 */\n        padding: 4px !important; /* 减小按钮内边距 */\n        margin: 0 1px !important; /* 减小按钮外边距 */\n        width: auto !important; /* 防止被设置为100%宽度 */\n        min-width: unset !important; /* 取消最小宽度限制 */\n        vertical-align: middle; /* 尝试垂直对齐 */\n        box-sizing: border-box; /* 确保 padding 和 border 不会增加总宽度 */\n        white-space: nowrap; /* 防止内部元素换行，确保图标横排 */\n    }\n\n    /* 特别针对这两个图标的父级按钮（如果它们是这样嵌套的） */\n    #nonQRFormItems button:has(>.fa-bars),\n    #nonQRFormItems button:has(>.fa-magic-wand-sparkles),\n    #nonQRFormItems a:has(>.fa-bars),\n    #nonQRFormItems a:has(>.fa-magic-wand-sparkles) {\n        /* 这些按钮的样式已在上面通过通用选择器覆盖 */\n    }\n\n    /* Assuming icons are direct children or within simple wrappers inside #nonQRFormItems */\n    /* If there's a specific container for left icons, target that */\n    /* For example, if HTML is <div id=\"nonQRFormItems\"><div class=\"left-icons\"><icon1/><icon2/></div>...</div> */\n    /* Then you'd target .left-icons { display: flex; flex-direction: row; align-items: center; } */\n    \n    /* General styling for buttons within the input bar to ensure they are inline and sized appropriately */\n    #nonQRFormItems .menu_button, /* Targeting generic menu buttons if they are used for icons */\n    #nonQRFormItems button { /* Or any button */\n        padding: 4px !important; /* 统一按钮内边距 */\n        margin: 0 1px !important; /* 统一按钮外边距 */\n        flex-shrink: 0; /* Prevent icons from shrinking too much */\n        display: inline-flex; /* Ensure proper inline behavior and internal flex formatting */\n        align-items: center; /* Vertically center icon within the button */\n        justify-content: center; /* Horizontally center icon within the button */\n    }\n     #nonQRFormItems .fa-bars,\n     #nonQRFormItems .fa-magic-wand-sparkles,\n     #nonQRFormItems .fa-paper-plane {\n        transform: scale(0.8); /* Slightly larger icons if original scale(0.7) was too small */\n     }\n\n\n    #nonQRFormItems > textarea { /* Ensure textarea takes up remaining space */\n        flex-grow: 1;\n        min-height: 20px; /* 设置最小高度为接近单行 */\n        height: 20px; /* 固定高度为单行 */\n        line-height: 20px; /* 确保文本在单行内垂直居中, 略小于高度以容纳边框等 */\n        padding-top: 0px; /* 微调垂直对齐 */\n        padding-bottom: 0px; /* 微调垂直对齐 */\n        resize: none; /* 禁止调整大小 */\n        overflow: hidden; /* 隐藏多余行内容, 强制单行输入感 */\n        box-sizing: border-box; /* 确保 padding 和 border 不会增加总宽度 */\n        margin-left: 25px !important; /* 调整左侧外边距以避免遮挡左侧按钮 */\n    }\n\n\n    .mes {\n        width: 100%; \n        box-sizing: border-box;\n        display: flex;\n        align-items: flex-start; \n        margin-bottom: 10px;\n    }\n\n    .mes[is_user=\"true\"] {\n        flex-direction: row-reverse;\n    }\n\n    .mes .mesAvatarWrapper {\n        flex-shrink: 0;\n        margin-top: 2px; \n    }\n\n    .mes:not([is_user=\"true\"]) .mesAvatarWrapper {\n        margin-right: 6px; /* Slightly reduced space */\n        margin-left: 0; \n    }\n\n    .mes[is_user=\"true\"] .mesAvatarWrapper {\n        margin-left: 6px; /* Slightly reduced space */\n        margin-right: 0; \n    }\n\n    .mes .mesAvatarWrapper .avatar {\n        width: 50px;  /* Further reduced avatar size */\n        height: 50px;\n        border-radius: 50%;\n    }\n\n    .mes .mes_text {\n        flex-grow: 1; \n        min-width: 0; \n        padding: 8px 12px !important; \n        font-size: 0.9rem; \n        line-height: 1.45; \n    }\n    .mes[is_user=\"true\"] .mes_text {\n        margin-left: 0; \n        margin-right: 0; \n    }\n\n    /* Side Panels - Adjusting top position and height to be below the top nav bar */\n    #right-nav-panel, #left-nav-panel {\n        position: fixed;\n        /* top: var(--nav-bar-width); */ /* Start below the nav bar */\n        /* We need to account for safe-area-inset-top as well for the nav bar's actual visual top */\n        top: calc(48px + env(safe-area-inset-top)); /* 调整面板顶部位置以适应新的顶栏高度 */\n        left: 0; \n        right: unset; \n        width: 85vw; \n        max-width: 340px; \n        /* height: calc(100vh - var(--nav-bar-width) - env(safe-area-inset-top)); */\n        /* Also account for bottom safe area if panel reaches bottom */\n        height: calc(100vh - (48px + env(safe-area-inset-top) + env(safe-area-inset-bottom)) ); /* 调整面板高度以适应新的顶栏高度 */\n        height: calc(100dvh - (48px + env(safe-area-inset-top) + env(safe-area-inset-bottom)) ); /* 调整面板高度以适应新的顶栏高度 */\n        background-color: var(--secondaryBg, #1c1c1e); \n        border-right: 1px solid var(--tertiaryBg, #303136); \n        border-left: none; \n        border-radius: 0; \n        z-index: 999; /* Below top-settings-holder (1000) so nav bar is clickable */\n        transform: translateX(-100%); \n        transition: transform 0.3s ease-in-out; \n        overflow-y: auto;\n        padding-top: 16px; /* 为面板内容添加上边距以避开导航栏 */\n        padding-bottom: env(safe-area-inset-bottom); /* Still needed if panel content scrolls to bottom */\n        box-sizing: border-box;\n    }\n\n    #right-nav-panel.openDrawer, #left-nav-panel.openDrawer {\n        transform: translateX(0); \n    }\n\n    body:has(#right-nav-panel.openDrawer), body:has(#left-nav-panel.openDrawer) {\n        overflow: hidden;\n    }\n\n    #rm_print_characters_block {\n        padding: 5px;\n    }\n    body.charListGrid #rm_print_characters_block .character_select .ch_name {\n        font-size: calc(var(--mainFontSize) * 0.9);\n    }\n\n    .mes .mes_buttons {\n        top: -10px; \n        right: 5px;\n        padding: 3px;\n        background: var(--tertiaryBg, #2c2c2e);\n    }\n    .mes .mes_buttons > * {\n        margin: 0 2px;\n        font-size: 0.9em; \n    }\n\n    #character_search_bar {\n        width: calc(100% - 16px);\n        position: static;\n        margin: 8px;\n    }\n    #avatar_div_div {\n        position: static;\n        margin: 8px auto;\n    }\n/* 用户设置界面移动端适配 */\n    .drawer-content {\n        width: 100%; /* 确保内容区域占满其父容器（面板）的宽度 */\n        box-sizing: border-box; /* padding 不会增加总宽度 */\n        padding-left: 16px;\n        padding-right: 16px;\n        padding-bottom: calc(env(safe-area-inset-bottom) + 16px);\n        overflow-y: auto; /* 确保内容可滚动 */\n    }\n\n    body #user-settings-block-content {\n        display: flex;\n        flex-direction: column;\n        align-items: stretch;\n        gap: 24px; /* 增加设置块之间的间距 */\n    }\n\n    body #user-settings-block-content > * {\n        flex: 1 1 auto !important;\n        min-width: 0;\n        width: 100% !important;\n        margin-bottom: 0; /* 使用 gap 代替 */\n    }\n    \n    body #user-settings-block-content > :nth-child(2) {\n        display: flex;\n        flex-direction: column;\n        flex-wrap: nowrap;\n        gap: 12px; /* 内部元素间距 */\n    }\n\n    body #user-settings-block-content #CustomCSS-block {\n        margin-left: 0;\n        flex-basis: auto !important;\n    }\n\n    body #user-settings-block-content #customCSS {\n        min-height: 200px; /* 调整CSS编辑框最小高度 */\n        font-size: 0.9em; /* 调整字体大小 */\n    }\n\n    #user-settings-block h4,\n    .standoutHeader {\n        font-size: 1.15em; /* 调整标题大小 */\n        margin-bottom: 12px;\n    }\n\n    /* 通用调整设置项内部布局 */\n    #user-settings-block .flex-container,\n    #user-settings-block .row.flex-container,\n    #user-settings-block .checkbox_label,\n    #user-settings-block .auto_continue_settings_block,\n    #user-settings-block #power-user-option-checkboxes > div {\n        flex-direction: column !important; /* 强制列布局 */\n        align-items: flex-start !important; /* 左对齐 */\n        gap: 8px; /* 元素间距 */\n        width: 100% !important; /* 确保这些容器也占满宽度 */\n        box-sizing: border-box; /* 确保 padding 不会增加总宽度 */\n    }\n    \n    #user-settings-block .flex-container > label,\n    #user-settings-block .flex-container > div,\n    #user-settings-block .flex-container > input,\n    #user-settings-block .flex-container > select,\n    #user-settings-block .flex-container > textarea,\n    #user-settings-block .checkbox_label > span,\n    #user-settings-block .checkbox_label > input[type=\"checkbox\"] {\n        width: auto !important; /* 允许元素根据内容调整宽度，或由父级控制 */\n        max-width: 100%; /* 防止溢出 */\n        margin-left: 0 !important;\n        margin-right: 0 !important;\n    }\n\n    #user-settings-block input[type=\"range\"] {\n        width: 100% !important; /* 滑块占满宽度 */\n    }\n    \n    #user-settings-block .neo-range-input { /* 滑块的数字输入框 */\n        width: 60px !important; /* 给数字输入框一个固定宽度 */\n    }\n\n    #user-settings-block .flex-container.flex-horizontal { /* 对于明确需要水平排列的，保持原样或特殊处理 */\n        flex-direction: row !important;\n        align-items: center !important;\n        gap: 8px;\n    }\n    #user-settings-block .flex-container.flex-horizontal > * {\n        width: auto !important; /* 允许子元素自动宽度 */\n    }\n}\n", "bogus_folders": true, "zoomed_avatar_magnification": false, "reduced_motion": true, "compact_input_area": false, "show_swipe_num_all_messages": false}