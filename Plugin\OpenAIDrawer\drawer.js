const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

const CACHE_DIR = path.join(__dirname, '.cache');

// 读取并解析从 stdin 传入的参数
async function readInput() {
    try {
        return new Promise((resolve, reject) => {
            let data = '';
            process.stdin.on('data', chunk => data += chunk);
            process.stdin.on('end', () => {
                try {
                    resolve(JSON.parse(data));
                } catch (e) {
                    reject(new Error(`Failed to parse input JSON: ${data}`));
                }
            });
        });
    } catch (e) {
        throw new Error(`Failed to read stdin: ${e.message}`);
    }
}

// 最终版：模仿你的成功范例来生成图片
async function generateAndDownloadImage(args, config) {
    const endpoint = `${config.apiBase}/chat/completions`;
    const headers = {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
    };
    
    const body = {
        model: config.model,
        messages: [
            { role: "system", content: "You are a helpful assistant." },
            { role: "user", content: args.prompt }
        ],
        stream: false
    };

    try {
        console.error("[DEBUG] 1. Sending request to API...");
        const response = await axios.post(endpoint, body, { headers });
        console.error("[DEBUG] 2. Received API response.");

        if (!response.data || !response.data.choices || !response.data.choices[0] || !response.data.choices[0].message) {
            throw new Error('Invalid API response structure. Full response: ' + JSON.stringify(response.data).substring(0, 500));
        }
        const responseText = response.data.choices[0].message.content;
        console.error(`[DEBUG] 3. API response text: ${responseText.substring(0, 200)}...`);

        const urlRegex = /!\[.*?\]\((.*?)\)/;
        const match = responseText.match(urlRegex);
        if (!match || !match[1]) {
            throw new Error('Could not find an image URL in the API response.');
        }
        const imageUrl = match[1];
        console.error(`[DEBUG] 4. Extracted image URL: ${imageUrl}`);

        const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
        console.error(`[DEBUG] 5. Downloaded image. Size: ${imageResponse.data.length} bytes.`);
        
        const imageBase64 = Buffer.from(imageResponse.data).toString('base64');
        console.error("[DEBUG] 6. Converted image to Base64.");
        
        return [imageBase64];

    } catch (error) {
        let detailedError = 'Plugin execution failed. ';
        if (error.response) {
            detailedError += `API Error (Status ${error.response.status}): ${JSON.stringify(error.response.data)}`;
        } else {
            detailedError += `Error: ${error.message}`;
        }
        // 将底层错误信息也打印到stderr，以防万一
        console.error(`[FATAL] ${detailedError}`);
        throw new Error(detailedError);
    }
}

// 主函数
async function main() {
    try {
        await fs.mkdir(CACHE_DIR, { recursive: true });

        const config = {
            apiKey: process.env.OPENAI_API_KEY,
            apiBase: process.env.OPENAI_API_BASE_URL,
            model: process.env.OPENAI_IMAGE_MODEL || 'wai-illustrious-fast',
        };

        if (!config.apiKey || !config.apiBase) {
            throw new Error('Configuration error: OPENAI_API_KEY and OPENAI_API_BASE_URL must be set.');
        }

        const inputArgs = await readInput();
        const args = { prompt: inputArgs.prompt };

        if (!args.prompt) {
            throw new Error("Input error: 'prompt' parameter is required.");
        }

        const cacheKey = `${args.prompt}|${config.model}`;
        const promptHash = crypto.createHash('sha256').update(cacheKey).digest('hex');
        const cacheFilePath = path.join(CACHE_DIR, `${promptHash}.b64`);
        
        let imagesBase64;
        try {
            const cachedData = await fs.readFile(cacheFilePath, 'utf-8');
            imagesBase64 = [cachedData];
            console.error("[DEBUG] Cache hit. Skipping API call.");
        } catch (e) {
            imagesBase64 = await generateAndDownloadImage(args, config);
            if (imagesBase64 && imagesBase64.length > 0) {
                await fs.writeFile(cacheFilePath, imagesBase64[0]);
            }
        }

        if (!imagesBase64 || imagesBase64.length === 0) {
            throw new Error("Failed to obtain image data from API or cache.");
        }

        // 根据前端代码分析，直接返回包含图片的HTML字符串
        const textResponse = `根据你的描述「${args.prompt}」，为你生成了 ${imagesBase64.length} 张图片。`;

        let htmlResult = `<p><strong>${textResponse}</strong></p>`;
        imagesBase64.forEach((b64, index) => {
            htmlResult += `<img src="data:image/png;base64,${b64}" alt="Generated Image ${index + 1}" width="400" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px; margin: 10px 0;" />`;
        });

        const result = { status: "success", result: htmlResult };
        console.log(JSON.stringify(result));

    } catch (error) {
        const errorResult = { status: "error", error: error.message };
        console.error(`[MAIN CATCH] Final error: ${error.message}`);
        console.log(JSON.stringify(errorResult));
    }
}

main();
