// This script should be injected BEFORE the main content_script.js
// It overrides window functions to intercept dialogs.

(function() {
    console.log('VCP Dialog Handler injected.');

    const originalAlert = window.alert;
    const originalConfirm = window.confirm;
    const originalPrompt = window.prompt;

    window.alert = function(message) {
        console.log('VCP: Intercepted alert:', message);
        window.postMessage({
            type: 'VCP_DIALOG_EVENT',
            dialogType: 'alert',
            message: message
        }, '*');
        // We don't call the original alert to prevent blocking the page
    };

    window.confirm = function(message) {
        console.log('VCP: Intercepted confirm:', message);
        window.postMessage({
            type: 'VCP_DIALOG_EVENT',
            dialogType: 'confirm',
            message: message
        }, '*');
        // To prevent blocking, we can return a default value (e.g., true)
        // or wait for a response from the AI, but for now, we'll just report and return true.
        return true;
    };

    window.prompt = function(message, defaultValue) {
        console.log('VCP: Intercepted prompt:', message);
        window.postMessage({
            type: 'VCP_DIALOG_EVENT',
            dialogType: 'prompt',
            message: message,
            defaultValue: defaultValue
        }, '*');
        // Return the default value or null to avoid blocking
        return defaultValue || null;
    };

})();
