{"name": "ChromeControl", "displayName": "Chrome 浏览器控制器", "version": "1.0.0", "description": "一个用于方便AI向Chrome浏览器发送操作指令（如点击、输入）的同步插件。", "pluginType": "synchronous", "entryPoint": {"command": "node ChromeControl.js"}, "communication": {"protocol": "stdio", "timeout": 15000}, "capabilities": {"invocationCommands": [{"command": "type", "description": "在指定的输入框中输入文本。\n- `command`: 固定为 `type`。\n- `target`: 元素的 `vcp-id`。\n- `text`: 要输入的文本内容。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」type「末」,\ntarget: 「始」vcp-id-123「末」,\ntext: 「始」VCP Agent是什么「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」type「末」,\ntarget: 「始」vcp-id-123「末」,\ntext: 「始」VCP Agent是什么「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "click", "description": "点击指定的按钮或链接。\n- `command`: 固定为 `click`。\n- `target`: 元素的 `vcp-id`。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」click「末」,\ntarget: 「始」vcp-id-456「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」click「末」,\ntarget: 「始」vcp-id-456「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "open_url", "description": "在新的标签页中打开指定的URL。\n- `command`: 固定为 `open_url`。\n- `url`: 要打开的完整URL地址。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」open_url「末」,\nurl: 「始」https://www.google.com「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」open_url「末」,\nurl: 「始」https://www.google.com「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "scroll", "description": "滚动页面或元素。\n- `command`: 固定为 `scroll`。\n- `target`: 要滚动到的目标的 `vcp-id`，或特殊值 'top', 'bottom'。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」scroll「末」,\ntarget: 「始」vcp-id-789「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」scroll「末」,\ntarget: 「始」bottom「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "hover", "description": "将鼠标悬停在指定的元素上，以触发下拉菜单或提示。\n- `command`: 固定为 `hover`。\n- `target`: 要悬停的元素的 `vcp-id`。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」hover「末」,\ntarget: 「始」vcp-id-101「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」hover「末」,\ntarget: 「始」vcp-id-101「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "list_tabs", "description": "列出当前浏览器窗口中所有打开的标签页。\n- `command`: 固定为 `list_tabs`。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」list_tabs「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」list_tabs「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "switch_tab", "description": "切换到指定的标签页。\n- `command`: 固定为 `switch_tab`。\n- `tabId`: 要切换到的标签页的ID (从 `list_tabs` 命令获取)。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」switch_tab「末」,\ntabId: 「始」123「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」switch_tab「末」,\ntabId: 「始」123「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "close_tab", "description": "关闭指定的标签页。\n- `command`: 固定为 `close_tab`。\n- `tabId`: 要关闭的标签页的ID (从 `list_tabs` 命令获取)。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」close_tab「末」,\ntabId: 「始」123「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」close_tab「末」,\ntabId: 「始」123「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "analyze_viewport", "description": "分析当前视口，返回一个带数字标签的截图和元素映射表。这允许AI通过视觉定位元素进行操作，特别适用于多模态模型。\n- `command`: 固定为 `analyze_viewport`。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」analyze_viewport「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」analyze_viewport「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}