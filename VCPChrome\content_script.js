let lastPageContent = '';
let vcpIdCounter = 0;
const activeWatchers = {}; // Object to hold active MutationObservers

// Listen for messages from the isolated world of the dialog_handler
window.addEventListener('message', (event) => {
    if (event.source === window && event.data.type === 'VCP_DIALOG_EVENT') {
        console.log('Content script received dialog event:', event.data);
        chrome.runtime.sendMessage({
            type: 'DIALOG_EVENT',
            data: event.data
        }).catch(e => console.error("Error sending dialog event to background:", e));
    }
}, false);


function isInteractive(node) {
    if (node.nodeType !== Node.ELEMENT_NODE) return false;
    const style = window.getComputedStyle(node);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0' || parseFloat(style.height) === 0 || parseFloat(style.width) === 0) return false;
    const rect = node.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) return false;
    const tagName = node.tagName.toLowerCase();
    const role = node.getAttribute('role');
    if (['a', 'button', 'input', 'textarea', 'select', 'option'].includes(tagName)) return true;
    if (role && ['button', 'link', 'checkbox', 'radio', 'menuitem', 'tab', 'switch', 'option', 'treeitem', 'searchbox', 'textbox', 'combobox'].includes(role)) return true;
    if (node.hasAttribute('onclick') || style.cursor === 'pointer') {
        if (tagName === 'body' || tagName === 'html') return false;
        if ((node.innerText || '').trim().length === 0 && node.children.length > 0 && !role) return false;
        return true;
    }
    if (node.hasAttribute('tabindex') && node.getAttribute('tabindex') !== '-1') return true;
    return false;
}

function isElementInViewport(el) {
    const rect = el.getBoundingClientRect();
    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= window.innerHeight && rect.right <= window.innerWidth;
}

function pageToMarkdown() {
    try {
        document.querySelectorAll('[vcp-id]').forEach(el => el.removeAttribute('vcp-id'));
        vcpIdCounter = 0;
        const body = document.body;
        if (!body) return '';
        let markdown = `# ${document.title}\nURL: ${document.URL}\n\n`;
        const ignoredTags = ['SCRIPT', 'STYLE', 'NAV', 'FOOTER', 'ASIDE', 'IFRAME', 'NOSCRIPT'];
        const processedNodes = new WeakSet();

        function processNode(node) {
            if (!node || processedNodes.has(node)) return '';
            if (node.nodeType === Node.ELEMENT_NODE) {
                const style = window.getComputedStyle(node);
                if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') return '';
                if (ignoredTags.includes(node.tagName)) return '';
            }
            if (node.parentElement && node.parentElement.closest('[vcp-id]')) return '';
            if (isInteractive(node)) {
                const interactiveMd = formatInteractiveElement(node);
                if (interactiveMd) {
                    processedNodes.add(node);
                    node.querySelectorAll('*').forEach(child => processedNodes.add(child));
                    return interactiveMd + '\n';
                }
            }
            if (node.nodeType === Node.TEXT_NODE) return node.textContent.replace(/\s+/g, ' ').trim() + ' ';
            let childContent = '';
            if (node.shadowRoot) childContent += processNode(node.shadowRoot);
            node.childNodes.forEach(child => childContent += processNode(child));
            if (node.nodeType === Node.ELEMENT_NODE && childContent.trim()) {
                const style = window.getComputedStyle(node);
                if (style.display === 'block' || style.display === 'flex' || style.display === 'grid') return '\n' + childContent.trim() + '\n';
            }
            return childContent;
        }
        markdown += processNode(body);
        return markdown.replace(/[ \t]+/g, ' ').replace(/ (\n)/g, '\n').replace(/(\n\s*){3,}/g, '\n\n').trim();
    } catch (e) {
        return `# ${document.title}\n\n[处理页面时出错: ${e.message}]`;
    }
}

function formatInteractiveElement(el) {
    if (el.hasAttribute('vcp-id')) return '';
    vcpIdCounter++;
    const vcpId = `vcp-id-${vcpIdCounter}`;
    el.setAttribute('vcp-id', vcpId);
    let text = (el.innerText || el.value || el.placeholder || el.ariaLabel || el.title || '').trim().replace(/\s+/g, ' ');
    const tagName = el.tagName.toLowerCase();
    const role = el.getAttribute('role');
    if (role === 'combobox' || role === 'searchbox' || tagName === 'input' && !['button', 'submit', 'reset', 'hidden'].includes(el.type)) {
        const label = findLabelForInput(el);
        return `[输入框: ${label || text || el.name || el.id || '无标题输入框'}](${vcpId})`;
    }
    if (tagName === 'a') return `[链接: ${text || '无标题链接'}](${vcpId})`;
    if (tagName === 'button' || role === 'button' || (tagName === 'input' && ['button', 'submit', 'reset'].includes(el.type))) return `[按钮: ${text || '无标题按钮'}](${vcpId})`;
    if (tagName === 'textarea') {
        const label = findLabelForInput(el);
        return `[文本区域: ${label || text || el.name || el.id || '无标题文本区域'}](${vcpId})`;
    }
    if (tagName === 'select') {
        const label = findLabelForInput(el);
        let options = Array.from(el.options).map(opt => `"${opt.text.trim()}" (value: ${opt.value})`).join(', ');
        return `[下拉选择: ${label || text || el.name || el.id || '无标题下拉框'}](${vcpId}) 可选值: [${options}]`;
    }
    if (text) return `[可交互元素: ${text}](${vcpId})`;
    const type = el.type || role || tagName;
    return `[可交互元素: 无文本 (${type})](${vcpId})`;
}

function findLabelForInput(inputElement) {
    if (inputElement.id) {
        const label = document.querySelector(`label[for="${inputElement.id}"]`);
        if (label) return label.innerText.trim();
    }
    const parentLabel = inputElement.closest('label');
    if (parentLabel) return parentLabel.innerText.trim();
    return null;
}

function sendPageInfoUpdate() {
    const currentPageContent = pageToMarkdown();
    if (currentPageContent && currentPageContent !== lastPageContent) {
        lastPageContent = currentPageContent;
        chrome.runtime.sendMessage({ type: 'PAGE_INFO_UPDATE', data: { markdown: currentPageContent } }).catch(err => {});
    }
}

function highlightElement(element) {
    const originalOutline = element.style.outline;
    const originalBoxShadow = element.style.boxShadow;
    element.style.outline = '3px solid #E91E63';
    element.style.boxShadow = '0 0 15px #E91E63';
    setTimeout(() => {
        element.style.outline = originalOutline;
        element.style.boxShadow = originalBoxShadow;
    }, 1500);
}

// --- Watcher Implementation ---
function handleWatchElement(commandData) {
    const { requestId, sourceClientId, target, condition, value, attribute } = commandData;
    let element = document.querySelector(`[vcp-id="${target}"]`);
    if (!element && condition !== 'appears') {
        return chrome.runtime.sendMessage({ type: 'COMMAND_RESULT', data: { requestId, sourceClientId, status: 'error', error: `watch_element: 找不到目标元素 ${target}` } });
    }

    const cleanup = () => {
        if (activeWatchers[requestId]) {
            activeWatchers[requestId].disconnect();
            delete activeWatchers[requestId];
        }
    };

    const checkCondition = () => {
        let currentElement = document.querySelector(`[vcp-id="${target}"]`);
        let conditionMet = false;
        switch (condition) {
            case 'appears': conditionMet = !!currentElement; break;
            case 'disappears': conditionMet = !currentElement; break;
            case 'text_contains': conditionMet = currentElement && currentElement.innerText.includes(value); break;
            case 'attribute_equals': conditionMet = currentElement && currentElement.getAttribute(attribute) === value; break;
            default:
                chrome.runtime.sendMessage({ type: 'COMMAND_RESULT', data: { requestId, sourceClientId, status: 'error', error: `不支持的观察条件: ${condition}` } });
                cleanup();
                return;
        }
        if (conditionMet) {
            chrome.runtime.sendMessage({ type: 'COMMAND_RESULT', data: { requestId, sourceClientId, status: 'success', message: `观察条件 '${condition}' 已满足。` } });
            cleanup();
        }
    };

    const observer = new MutationObserver(checkCondition);
    observer.observe(document.body, { childList: true, subtree: true, attributes: true, characterData: true });
    activeWatchers[requestId] = observer;
    checkCondition();
}

function handleCancelWatch(commandData) {
    const { requestId } = commandData;
    if (activeWatchers[requestId]) {
        activeWatchers[requestId].disconnect();
        delete activeWatchers[requestId];
        chrome.runtime.sendMessage({ type: 'COMMAND_RESULT', data: { requestId, status: 'success', message: '观察者已取消。' } });
    }
}

// --- Command Handlers & Message Listener ---
function handleGetInteractiveElementsInfo() {
    const elements = document.querySelectorAll('a, button, input, textarea, select, [role="button"], [role="link"]');
    const interactiveElementsInfo = [];
    let labelCounter = 1;
    elements.forEach(el => {
        if (isInteractive(el) && isElementInViewport(el)) {
            const rect = el.getBoundingClientRect();
            if (!el.hasAttribute('vcp-id')) {
                 vcpIdCounter++;
                 el.setAttribute('vcp-id', `vcp-id-${vcpIdCounter}`);
            }
            const vcpId = el.getAttribute('vcp-id');
            const text = (el.innerText || el.value || el.placeholder || el.ariaLabel || el.title || '').trim().replace(/\s+/g, ' ');
            interactiveElementsInfo.push({ vcpId, label: (labelCounter++).toString(), bounds: { x: rect.x, y: rect.y, width: rect.width, height: rect.height }, text: text || `[${el.tagName.toLowerCase()}]` });
        }
    });
    return { status: 'success', elements: interactiveElementsInfo };
}
function handleGetTables() {
    const tables = Array.from(document.querySelectorAll('table'));
    const jsonTables = tables.map(table => {
        const headers = Array.from(table.querySelectorAll('th')).map(th => th.innerText.trim());
        const rows = Array.from(table.querySelectorAll('tr'));
        return rows.map(row => {
            const cells = Array.from(row.querySelectorAll('td'));
            if (headers.length > 0 && cells.length > 0) {
                let obj = {};
                cells.forEach((cell, index) => {
                    const header = headers[index] || `column_${index + 1}`;
                    obj[header] = cell.innerText.trim();
                });
                return obj;
            } else if (cells.length > 0) return cells.map(cell => cell.innerText.trim());
            return null;
        }).filter(row => row && Object.keys(row).length > 0);
    });
    return { status: 'success', tables: jsonTables };
}
function handleGetLists() {
    const lists = Array.from(document.querySelectorAll('ul, ol'));
    const jsonLists = lists.map(list => Array.from(list.querySelectorAll(':scope > li')).map(li => li.innerText.trim()));
    return { status: 'success', lists: jsonLists };
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'CLEAR_STATE') lastPageContent = '';
    else if (request.type === 'REQUEST_PAGE_INFO_UPDATE') sendPageInfoUpdate();
    else if (request.type === 'EXECUTE_COMMAND') {
        const { command, target, text, value, attributes: attributesToGet, requestId, sourceClientId } = request.data;
        let result = {};
        try {
            if (command === 'watch_element') return handleWatchElement(request.data);
            if (command === 'cancel_watch') return handleCancelWatch(request.data);
            if (command === 'get_tables') result = handleGetTables();
            else if (command === 'get_lists') result = handleGetLists();
            else if (command === 'get_interactive_elements_info') result = handleGetInteractiveElementsInfo();
            else {
                let element;
                if (target) {
                    element = document.querySelector(`[vcp-id="${target}"]`);
                    if (!element) {
                        const allInteractiveElements = document.querySelectorAll('[vcp-id]');
                        for (const el of allInteractiveElements) {
                            const elText = (el.innerText || el.value || el.placeholder || el.ariaLabel || el.title || '').trim().replace(/\s+/g, ' ');
                            if (elText && elText.includes(target)) { element = el; break; }
                        }
                    }
                }
                if (target && !element) throw new Error(`未能在页面上找到目标为 '\'${target}\' 的元素。`);
                if (element) highlightElement(element);
                switch (command) {
                    case 'type':
                        if (!element) throw new Error("Type command requires a target element.");
                        if (element.isContentEditable) {
                            element.focus();
                            document.execCommand('insertText', false, text);
                            result = { status: 'success', message: `成功在富文本编辑器中输入文本。` };
                        } else if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                            element.value = text;
                            result = { status: 'success', message: `成功在目标元素中输入文本。` };
                        } else {
                            throw new Error(`目标元素既不是输入框/文本区域，也不是可编辑元素。`);
                        }
                        break;
                    case 'click':
                        if (!element) throw new Error("Click command requires a target element.");
                        element.focus();
                        element.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true, view: window }));
                        result = { status: 'success', message: `成功点击了目标元素。` };
                        break;
                    case 'hover':
                        if (!element) throw new Error("Hover command requires a target element.");
                        element.focus();
                        element.dispatchEvent(new MouseEvent('mouseover', { bubbles: true, cancelable: true, view: window }));
                        element.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true, cancelable: true, view: window }));
                        result = { status: 'success', message: `成功悬停在目标元素上。` };
                        break;
                    case 'scroll':
                        if (element) {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            result = { status: 'success', message: `成功滚动到目标元素。` };
                        } else if (target === 'top') {
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                            result = { status: 'success', message: `成功滚动到页面顶部。` };
                        } else if (target === 'bottom') {
                            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                            result = { status: 'success', message: `成功滚动到页面底部。` };
                        } else throw new Error("Scroll command requires a valid target ('vcp-id', 'top', or 'bottom').");
                        break;
                    case 'select_option':
                        if (!element) throw new Error("select_option command requires a target element.");
                        if (element.tagName !== 'SELECT') throw new Error("目标元素不是一个下拉选择框。");
                        let optionToSelect = Array.from(element.options).find(opt => opt.value === value || opt.text.trim() === text);
                        if (optionToSelect) {
                            element.value = optionToSelect.value;
                            element.dispatchEvent(new Event('change', { bubbles: true }));
                            result = { status: 'success', message: `成功在下拉框中选择选项 '${optionToSelect.text}'。` };
                        } else throw new Error(`在下拉框中未找到值为 '\'${value}\' 或文本为 '\'${text}\' 的选项。`);
                        break;
                    case 'get_attributes':
                        if (!element) throw new Error("get_attributes command requires a target element.");
                        const defaultAttributes = ['href', 'value', 'src', 'alt', 'title', 'placeholder', 'data-*'];
                        const finalAttributesToGet = attributesToGet && attributesToGet.length > 0 ? attributesToGet : defaultAttributes;
                        const attributes = {};
                        for (const attr of finalAttributesToGet) {
                            if (attr === 'data-*') {
                                 for(const dataAttr of element.attributes) if(dataAttr.name.startsWith('data-')) attributes[dataAttr.name] = dataAttr.value;
                            } else if (element.hasAttribute(attr)) attributes[attr] = element.getAttribute(attr);
                        }
                        result = { status: 'success', message: `成功获取目标元素的属性。`, attributes };
                        break;
                    default: throw new Error(`不支持的命令: ${command}`);
                }
            }
        } catch (error) {
            result = { status: 'error', error: error.message };
        }
        chrome.runtime.sendMessage({ type: 'COMMAND_RESULT', data: { requestId, sourceClientId, ...result } });
        setTimeout(sendPageInfoUpdate, 500);
    }
});

const debouncedSendPageInfoUpdate = debounce(sendPageInfoUpdate, 500);
const observer = new MutationObserver(() => debouncedSendPageInfoUpdate());
observer.observe(document.body, { childList: true, subtree: true, attributes: true, characterData: true });
document.addEventListener('click', debouncedSendPageInfoUpdate);
document.addEventListener('focusin', debouncedSendPageInfoUpdate);
document.addEventListener('scroll', debouncedSendPageInfoUpdate, true);
window.addEventListener('load', sendPageInfoUpdate);
document.addEventListener('visibilitychange', () => { if (document.visibilityState === 'visible') sendPageInfoUpdate(); });
setInterval(sendPageInfoUpdate, 5000);
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => { clearTimeout(timeout); func(...args); };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}