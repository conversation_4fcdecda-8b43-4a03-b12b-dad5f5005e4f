#!/usr/bin/env python3
"""
测试脚本：验证生成的图片是否有效
"""
import sys
import json
import base64
import tempfile
import os

def test_generated_image():
    # 测试输入
    test_input = '{"chart_code": "graph TD; A[测试]-->B[成功];"}'
    
    # 模拟插件调用
    import subprocess
    result = subprocess.run(
        [sys.executable, 'MermaidGenerator.py'],
        input=test_input.encode('utf-8'),
        capture_output=True,
        text=False  # 使用二进制模式避免编码问题
    )
    
    if result.returncode != 0:
        stderr_text = result.stderr.decode('utf-8', errors='ignore') if result.stderr else 'None'
        print(f"插件执行失败: {stderr_text}")
        return False

    try:
        stdout_text = result.stdout.decode('utf-8', errors='ignore')
        output = json.loads(stdout_text)
        if output['status'] != 'success':
            print(f"插件返回错误: {output.get('error', '未知错误')}")
            return False
        
        # 检查Base64数据
        base64_data = output.get('base64', '')
        if not base64_data:
            print("没有返回Base64数据")
            return False
        
        # 验证Base64数据是否有效
        try:
            img_data = base64.b64decode(base64_data)
            print(f"✅ Base64解码成功，图片大小: {len(img_data)} 字节")
            
            # 检查PNG文件头
            if img_data.startswith(b'\x89PNG\r\n\x1a\n'):
                print("✅ PNG文件头验证成功")
            else:
                print("❌ PNG文件头验证失败")
                return False
            
            # 保存测试图片
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
                f.write(img_data)
                test_file = f.name
            
            print(f"✅ 测试图片已保存到: {test_file}")
            print(f"✅ 图片文件大小: {os.path.getsize(test_file)} 字节")
            
            return True
            
        except Exception as e:
            print(f"❌ Base64解码失败: {e}")
            return False
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        stdout_text = result.stdout.decode('utf-8', errors='ignore') if isinstance(result.stdout, bytes) else result.stdout
        print(f"原始输出: {stdout_text}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试 MermaidGenerator 插件...")
    success = test_generated_image()
    if success:
        print("🎉 所有测试通过！")
    else:
        print("💥 测试失败！")
        sys.exit(1)
