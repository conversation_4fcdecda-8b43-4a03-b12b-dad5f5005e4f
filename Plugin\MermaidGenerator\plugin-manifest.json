{"manifestVersion": "1.0.0", "name": "MermaidGenerator", "version": "2.0.0", "displayName": "Mermaid图表渲染器", "description": "一个将Mermaid代码在服务器端直接渲染成PNG图片的工具。AI的核心任务是根据用户的请求，自行生成符合Mermaid语法的图表代码。插件会返回一张渲染好的图片。", "author": "Your Assistant", "pluginType": "synchronous", "entryPoint": {"type": "python", "command": "python MermaidGenerator.py"}, "communication": {"protocol": "stdio", "timeout": 15000}, "capabilities": {"invocationCommands": [{"commandIdentifier": "GenerateChart", "description": "当用户需要可视化流程、计划或结构时，使用此工具。你的任务是：1. 理解用户的需求。 2. 自行编写出符合Mermaid语法的图表代码。 3. 将完整的Mermaid代码作为参数调用此工具，插件会返回一张PNG图片。\n支持的图表类型包括但不限于：流程图(flowchart)、思维导图(mindmap)、序列图(sequenceDiagram)、甘特图(gantt)等。\n参数:\n- chart_code (字符串, 必需): 你生成的完整Mermaid图表代码。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MermaidGenerator「末」,\nchart_code:「始」graph TD; A-->B;「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "用户请求: '画一个A到B再到C的简单流程图'\nAI生成的调用:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MermaidGenerator「末」,\nchart_code:「始」flowchart TD\n    A[开始] --> B(处理过程)\n    B --> C{结束}「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}