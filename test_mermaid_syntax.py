#!/usr/bin/env python3
"""
测试脚本：验证 MermaidGenerator 插件对各种语法的处理能力
"""
import json
import subprocess
import sys
import os

def test_mermaid_syntax(chart_code, description):
    """测试单个 Mermaid 语法"""
    print(f"\n🧪 测试: {description}")
    print(f"📝 代码: {chart_code[:100]}...")
    
    try:
        # 执行插件
        result = subprocess.run(
            [sys.executable, 'MermaidGenerator.py'],
            input=json.dumps({"chart_code": chart_code}),
            capture_output=True,
            text=True,
            cwd='Plugin/MermaidGenerator'
        )
        
        if result.returncode != 0:
            print(f"❌ 插件执行失败: {result.stderr}")
            return False
        
        # 解析输出
        try:
            output = json.loads(result.stdout)
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return False
        
        # 检查结果
        if output.get('status') == 'success':
            result_content = output.get('result', '')
            if '<img' in result_content and 'data:image/png;base64,' in result_content:
                print("✅ 测试通过")
                return True
            else:
                print("❌ 结果中没有有效的图片")
                return False
        else:
            print(f"❌ 插件返回错误: {output.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 MermaidGenerator 插件的语法处理能力...")
    
    # 定义测试用例
    test_cases = [
        {
            'code': 'graph TD; A-->B; B-->C;',
            'description': '基本流程图'
        },
        {
            'code': 'flowchart LR\n    A[开始] --> B{判断}\n    B -->|是| C[执行]\n    B -->|否| D[跳过]',
            'description': '带中文的流程图'
        },
        {
            'code': 'classDef afternoon fill:#2ecc71,stroke:#27ae60,color:#fff,stroke-width:2px',
            'description': '只有样式定义（应该被自动包装）'
        },
        {
            'code': 'A-->B\nB-->C\nclassDef default fill:#f9f9f9',
            'description': '节点定义 + 样式定义'
        },
        {
            'code': 'sequenceDiagram\n    Alice->>Bob: Hello Bob, how are you?\n    Bob-->>John: How about you John?\n    Bob--x Alice: I am good thanks!',
            'description': '序列图'
        },
        {
            'code': 'pie title 宠物分布\n    "狗" : 386\n    "猫" : 85\n    "鼠" : 15',
            'description': '饼图'
        },
        {
            'code': 'gantt\n    title 项目计划\n    dateFormat  YYYY-MM-DD\n    section 设计\n    需求分析      :done,    des1, 2014-01-06,2014-01-08\n    原型设计      :active,  des2, 2014-01-09, 3d',
            'description': '甘特图'
        },
        {
            'code': 'graph TD\n    A[Christmas] -->|Get money| B(Go shopping)\n    B --> C{Let me think}\n    C -->|One| D[Laptop]\n    C -->|Two| E[iPhone]\n    C -->|Three| F[fa:fa-car Car]',
            'description': '复杂流程图'
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for test_case in test_cases:
        success = test_mermaid_syntax(test_case['code'], test_case['description'])
        if success:
            success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 个测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！")
        print("\n✨ MermaidGenerator 插件现在支持:")
        print("- 自动修复缺少图表类型声明的代码")
        print("- 正确处理 classDef 样式定义")
        print("- 支持各种 Mermaid 图表类型")
        print("- 提供友好的错误信息")
        print("- 返回前端兼容的 HTML 格式")
        return True
    else:
        print("💥 部分测试失败！")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
