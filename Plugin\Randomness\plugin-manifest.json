{"manifestVersion": "1.0.0", "name": "Randomness", "displayName": "随机事件生成器", "version": "5.2.0", "description": "一个多功能后端插件，用于生成各种可信的随机事件。支持无状态的单次随机事件（如抽牌、掷骰）和有状态的、可持久化的牌堆管理（创建、抽取、重置、销毁），适用于需要连续操作的场景。", "author": "VincentHDLee & Gemini", "pluginType": "synchronous", "entryPoint": {"type": "python", "command": "python main.py"}, "communication": {"protocol": "stdio", "timeout": 10000}, "configSchema": {"TAROT_DECK_PATH": {"type": "string", "description": "Path to the tarot deck JSON data file.", "default": "Plugin/Randomness/data/tarot_deck.json"}, "RUNE_SET_PATH": {"type": "string", "description": "Path to the rune set JSON data file.", "default": "Plugin/Randomness/data/rune_set.json"}, "POKER_DECK_PATH": {"type": "string", "description": "Path to the poker deck JSON data file.", "default": "Plugin/Randomness/data/poker_deck.json"}, "TAROT_SPREADS_PATH": {"type": "string", "description": "Path to the tarot spreads JSON data file.", "default": "Plugin/Randomness/data/tarot_spreads.json"}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "createDeck", "description": "创建一个新的、有状态的牌堆实例，用于后续的连续抽牌操作。\n参数:\n- deckName (字符串, 必需): 要创建的牌堆类型。可用选项: 'poker', 'tarot'。\n- deckCount (整数, 可选, 默认=1): 要混合在一起的牌堆数量。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」createDeck「末」,\ndeckName:「始」poker「末」,\ndeckCount:「始」2「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "createCustomDeck", "description": "根据用户提供的任意卡牌列表创建一个新的、有状态的自定义牌堆实例。\n参数:\n- cards (数组, 必需): 一个包含自定义卡牌名称的JSON数组字符串。例如: '[\"神引\", \"天启\", \"命运\"]'。\n- deckName (字符串, 可选, 默认='custom'): 为这个自定义牌堆指定的名称。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」createCustomDeck「末」,\ncards:「始」[\"攻击\", \"防御\", \"闪避\"]「末」,\ndeckName:「始」战斗卡牌「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "drawFromDeck", "description": "从一个已创建的、有状态的牌堆实例中抽取指定数量的牌。\n参数:\n- deckId (字符串, 必需): 由 createDeck 或 createCustomDeck 命令返回的牌堆唯一ID。\n- count (整数, 可选, 默认=1): 要抽取的牌的数量。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」drawFromDeck「末」,\ndeckId:「始」...「末」,\ncount:「始」3「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "resetDeck", "description": "重置一个指定的牌堆实例，将所有已抽出的牌放回牌堆并重新洗牌。\n参数:\n- deckId (字符串, 必需): 要重置的牌堆的唯一ID。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」resetDeck「末」,\ndeckId:「始」...「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "destroyDeck", "description": "销毁一个指定的牌堆实例，将其从内存中移除以释放资源。\n参数:\n- deckId (字符串, 必需): 要销毁的牌堆的唯一ID。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」destroyDeck「末」,\ndeckId:「始」...「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "queryDeck", "description": "查询一个指定的有状态牌堆实例的当前状态（剩余牌数、已抽牌数等）。\n参数:\n- deckId (字符串, 必需): 要查询的牌堆的唯一ID。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」queryDeck「末」,\ndeckId:「始」...「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "getCards", "description": "[无状态] 从一个指定类型的完整牌堆中，进行一次性的洗牌并抽取指定数量的牌。\n参数:\n- deckName (字符串, 必需): 要使用的牌堆类型。可用选项: 'poker', 'tarot'。\n- count (整数, 可选, 默认=1): 要抽取的牌的数量。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」getCards「末」,\ndeckName:「始」tarot「末」,\ncount:「始」1「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "rollDice", "description": "执行一个复杂的TRPG风格的掷骰表达式，支持加减乘除、括号、取高/低、优势/劣势、CoC奖惩骰等。\n参数:\n- diceString (字符串, 必需): 要执行的掷骰表达式。例如 '2d6+5', '(4d6kh3+2)*10', '1d20adv', '1d100bp2'。\n- format (字符串, 可选, 默认='text'): 输出格式。'text' (默认) 或 'ascii' (仅对d6生效)。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」rollDice「末」,\ndiceString:「始」(2d8+1d6)kh2+5「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "drawTarot", "description": "[无状态] 从塔罗牌库中抽牌，支持多种预设牌阵或指定抽牌数量。\n参数:\n- spread (字符串, 可选): 要使用的牌阵名称。如果提供，将忽略 'count' 参数。\n- count (整数, 可选, 默认=3): 在不使用预设牌阵时，要抽取的牌的数量。\n- allowReversed (布尔, 可选, 默认=true): 是否允许出现逆位牌。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」drawTarot「末」,\nspread:「始」three_card「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "castRunes", "description": "[无状态] 从符文集中抽取指定数量的卢恩符文。\n参数:\n- count (整数, 可选, 默认=1): 要抽取的卢恩符文数量。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」castRunes「末」,\ncount:「始」3「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "selectFromList", "description": "[无状态] 从一个给定的任意列表中随机抽取一个或多个项目。\n参数:\n- items (数组, 必需): 一个包含待选项的JSON数组字符串。例如: '[\"苹果\", \"香蕉\", \"橙子\"]'。\n- count (整数, 可选, 默认=1): 要选择的项目的数量。\n- withReplacement (布尔, 可选, 默认=false): 是否允许重复抽取（有放回的抽取）。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」selectFromList「末」,\nitems:「始」[\"选项A\", \"选项B\", \"选项C\"]「末」,\ncount:「始」2「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"commandIdentifier": "getRandomDateTime", "description": "[无状态] 在一个指定的开始和结束日期/时间范围内，生成一个随机的时间点。\n参数:\n- start (字符串, 可选): ISO 8601 格式的起始时间。默认为1970年。\n- end (字符串, 可选): ISO 8601 格式的结束时间。默认为当前时间。\n- format (字符串, 可选): 输出的日期时间格式。默认为 '%Y-%m-%d %H:%M:%S'。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Randomness「末」,\ncommand:「始」getRandomDateTime「末」,\nstart:「始」2024-01-01T00:00:00Z「末」,\nend:「始」2024-12-31T23:59:59Z「末」\n<<<[END_TOOL_REQUEST]>>>"}]}, "webSocketPush": {"enabled": true, "usePluginResultAsMessage": false, "messageType": "RandomEventResult"}}